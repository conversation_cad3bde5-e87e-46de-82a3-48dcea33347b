# """
# 可视化模块
# 提供各种数据可视化功能，包括传感器数据和模型结果的可视化
# """

# import numpy as np
# import matplotlib.pyplot as plt
# import matplotlib.tri as mtri
# from matplotlib import cm
# import seaborn as sns
# import os

# # 设置字体和样式（无需中文字体，但保留兼容性）
# plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
# plt.rcParams['axes.unicode_minus'] = False
# sns.set_style("whitegrid")

# class ModelVisualizer:
#     """模型可视化类"""
    
#     def __init__(self, figsize=(12, 8), dpi=300):
#         """
#         Initialize the visualizer
        
#         Args:
#             figsize (tuple): Default figure size
#             dpi (int): Figure resolution
#         """
#         self.figsize = figsize
#         self.dpi = dpi
#         self.color_maps = {
#             'temperature': 'coolwarm',
#             'ph': 'RdYlBu_r',
#             'conductivity': 'viridis',
#             'chlorophyll': 'Greens',
#             'health': 'RdYlGn'
#         }
    
#     def create_directory(self, path):
#         """Create directory if it doesn't exist"""
#         if not os.path.exists(path):
#             os.makedirs(path)

#     # visualization.py in class ModelVisualizer

#     def plot_sensor_data_2d(self, node_coords, sensor_data, elements, save_path=None):
#         """
#         Plot 2D surface plots of sensor data
        
#         Args:
#             node_coords (np.array): Node coordinates (N, 3)
#             sensor_data (dict): Sensor data
#             elements (np.array): Triangle elements (M, 3)
#             save_path (str): Save path
#         """
#         # 创建三角化对象（仅使用 X, Y 坐标）
#         # 注意：gmsh的单元是从1开始索引的，python是从0开始，所以要减1
#         triang = mtri.Triangulation(node_coords[:, 0], node_coords[:, 1], elements[:, :3] - 1)
        
#         fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
#         parameters = ['temperature', 'ph', 'conductivity', 'chlorophyll']
#         titles = ['Temperature Distribution (°C)', 'pH Distribution', 
#                  'Conductivity Distribution (μS/cm)', 'Chlorophyll Distribution (μg/L)']
        
#         for i, (param, title) in enumerate(zip(parameters, titles)):
#             ax = axes[i//2, i%2]
            
#             # --- 核心修改在这里 ---
#             # 旧代码:
#             # tcf = ax.tripcolor(triang, sensor_data[param], cmap=self.color_maps[param], shading='gouraud')
            
#             # 新代码: 使用 tricontourf 实现平滑填充
#             # levels=100 参数让颜色过渡更平滑
#             tcf = ax.tricontourf(triang, sensor_data[param], levels=100, cmap=self.color_maps[param])
#             # ---------------------

#             ax.set_xlabel('X Coordinate (m)')
#             ax.set_ylabel('Y Coordinate (m)')
#             ax.set_title(title)
#             ax.set_aspect('equal')
            
#             # 添加颜色条
#             cbar = plt.colorbar(tcf, ax=ax)
#             cbar.set_label(param.capitalize())
            
#             # 添加统计信息
#             data = sensor_data[param]
#             stats_text = f'Mean: {np.mean(data):.2f}\nStandard Deviation: {np.std(data):.2f}'
#             ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
#                    verticalalignment='top', bbox=dict(boxstyle='round', 
#                    facecolor='white', alpha=0.8))
        
#         plt.tight_layout()
        
#         if save_path:
#             self.create_directory(save_path)
#             plt.savefig(f'{save_path}/sensor_data_2d.png', 
#                        dpi=self.dpi, bbox_inches='tight')
#         plt.show()
    
    

#     # visualization.py in class ModelVisualizer

#     def plot_health_index_2d(self, node_coords, health_index, elements, save_path=None):
#         """
#         Plot 2D surface plot of health index
        
#         Args:
#             node_coords (np.array): Node coordinates (N, 3)
#             health_index (np.array): Health index
#             elements (np.array): Triangle elements (M, 3)
#             save_path (str): Save path
#         """
#         # 创建三角化对象
#         triang = mtri.Triangulation(node_coords[:, 0], node_coords[:, 1], elements[:, :3] - 1)
        
#         fig, ax = plt.subplots(figsize=self.figsize)
        
#         # --- 核心修改在这里 ---
#         # 旧代码:
#         # tcf = ax.tripcolor(triang, health_index, cmap=self.color_maps['health'], 
#         #                   shading='gouraud', vmin=0, vmax=1)

#         # 新代码: 使用 tricontourf 实现平滑填充
#         tcf = ax.tricontourf(triang, health_index, levels=150, cmap=self.color_maps['health'], 
#                              vmin=0, vmax=1)
#         # ---------------------

#         ax.set_xlabel('X Coordinate (m)')
#         ax.set_ylabel('Y Coordinate (m)')
#         ax.set_title('Pearl Mussel Health Index Distribution')
#         ax.set_aspect('equal')
        
#         # 添加颜色条
#         cbar = plt.colorbar(tcf, ax=ax)
#         cbar.set_label('Health Index')
        
#         # 添加统计信息
#         stats_text = f'Mean: {np.mean(health_index):.3f}\n' \
#                     f'Standard Deviation: {np.std(health_index):.3f}\n' \
#                     f'Minimum: {np.min(health_index):.3f}\n' \
#                     f'Maximum: {np.max(health_index):.3f}'
#         ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
#                verticalalignment='top', bbox=dict(boxstyle='round', 
#                facecolor='white', alpha=0.9))
        
#         plt.tight_layout()
        
#         if save_path:
#             self.create_directory(save_path)
#             plt.savefig(f'{save_path}/health_index_2d.png', 
#                        dpi=self.dpi, bbox_inches='tight')
#         plt.show()
    
    
#     def plot_3d_visualization(self, node_coords, data, title, colormap, save_path=None):
#         """
#         Create 3D scatter plot visualization
        
#         Args:
#             node_coords (np.array): Node coordinates
#             data (np.array): Data to visualize
#             title (str): Figure title
#             colormap (str): Colormap
#             save_path (str): Save path
#         """
#         fig = plt.figure(figsize=(12, 9))
#         ax = fig.add_subplot(111, projection='3d')
        
#         scatter = ax.scatter(node_coords[:, 0], node_coords[:, 1], node_coords[:, 2],
#                            c=data, cmap=colormap, s=30, alpha=0.6)
        
#         ax.set_xlabel('X Coordinate (m)')
#         ax.set_ylabel('Y Coordinate (m)')
#         ax.set_zlabel('Z Coordinate (m)')
#         ax.set_title('Pearl Mussel Health Index 3D Distribution')
        
#         # 添加颜色条
#         cbar = plt.colorbar(scatter, ax=ax, shrink=0.5, aspect=20)
#         cbar.set_label('Health Index')
        
#         plt.tight_layout()
        
#         if save_path:
#             self.create_directory(save_path)
#             filename = title.replace(' ', '_').replace('(', '').replace(')', '').lower()
#             plt.savefig(f'{save_path}/{filename}_3d.png', 
#                        dpi=self.dpi, bbox_inches='tight')
#         plt.show()
    
#     def plot_correlation_matrix(self, sensor_data, health_index, save_path=None):
#         """
#         Plot correlation matrix of parameters
        
#         Args:
#             sensor_data (dict): Sensor data
#             health_index (np.array): Health index
#             save_path (str): Save path
#         """
#         # 准备数据
#         data_dict = sensor_data.copy()
#         data_dict['health_index'] = health_index
        
#         # 创建DataFrame用于相关性分析
#         import pandas as pd
#         df = pd.DataFrame({
#             'Temperature': data_dict['temperature'],
#             'pH': data_dict['ph'],
#             'Conductivity': data_dict['conductivity'],
#             'Chlorophyll': data_dict['chlorophyll'],
#             'Health Index': data_dict['health_index']
#         })
        
#         # 计算相关性矩阵
#         correlation_matrix = df.corr()
        
#         # 绘制热力图
#         fig, ax = plt.subplots(figsize=(10, 8))
#         sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
#                    square=True, ax=ax, cbar_kws={'label': 'Correlation Coefficient'})
#         ax.set_title('Correlation Analysis of Environmental Parameters and Health Index')
        
#         plt.tight_layout()
        
#         if save_path:
#             self.create_directory(save_path)
#             plt.savefig(f'{save_path}/correlation_matrix.png', 
#                        dpi=self.dpi, bbox_inches='tight')
#         plt.show()
    
#     def plot_parameter_distributions(self, sensor_data, health_index, save_path=None):
#         """
#         Plot histogram of parameter distributions
        
#         Args:
#             sensor_data (dict): Sensor data
#             health_index (np.array): Health index
#             save_path (str): Save path
#         """
#         fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
#         parameters = ['temperature', 'ph', 'conductivity', 'chlorophyll']
#         titles = ['Temperature (°C)', 'pH', 'Conductivity (μS/cm)', 'Chlorophyll (μg/L)']
        
#         # 绘制传感器数据分布
#         for i, (param, title) in enumerate(zip(parameters, titles)):
#             ax = axes[i//2, i%2]
#             data = sensor_data[param]
            
#             ax.hist(data, bins=30, alpha=0.7, edgecolor='black', 
#                    color=plt.cm.Set3(i))
#             ax.set_title(f'{title} Distribution')
#             ax.set_xlabel(title)
#             ax.set_ylabel('Frequency')
#             ax.grid(True, alpha=0.3)
            
#             # 添加统计线
#             mean_val = np.mean(data)
#             ax.axvline(mean_val, color='red', linestyle='--', 
#                       label=f'Mean: {mean_val:.2f}')
#             ax.legend()
        
#         # 绘制健康度指数分布
#         ax = axes[1, 2]
#         ax.hist(health_index, bins=30, alpha=0.7, edgecolor='black', 
#                color='lightgreen')
#         ax.set_title('Health Index Distribution')
#         ax.set_xlabel('Health Index')
#         ax.set_ylabel('Frequency')
#         ax.grid(True, alpha=0.3)
        
#         mean_health = np.mean(health_index)
#         ax.axvline(mean_health, color='red', linestyle='--', 
#                   label=f'Mean: {mean_health:.3f}')
#         ax.legend()
        
#         plt.tight_layout()
        
#         if save_path:
#             self.create_directory(save_path)
#             plt.savefig(f'{save_path}/parameter_distributions.png', 
#                        dpi=self.dpi, bbox_inches='tight')
#         plt.show()
    
#     # def create_comprehensive_visualization(self, node_coords, sensor_data, health_index, elements, save_path=None):
#     def create_comprehensive_visualization(self, node_coords, sensor_data, health_index=None, elements=None, save_path='results', is_prediction=False):
#     # ... 函数内容 ...
#         """
#         Create comprehensive visualization report
        
#         Args:
#             node_coords (np.array): Node coordinates
#             sensor_data (dict): Sensor data
#             health_index (np.array): Health index
#             elements (np.array): Triangle elements
#             save_path (str): Save path
#         """
#         print("Generating 2D sensor data surface plots...")
#         self.plot_sensor_data_2d(node_coords, sensor_data, elements, save_path)
        
#         print("Generating 2D health index surface plot...")
#         self.plot_health_index_2d(node_coords, health_index, elements, save_path)
        
#         print("Generating parameter distribution histograms...")
#         self.plot_parameter_distributions(sensor_data, health_index, save_path)
        
#         print("Generating correlation analysis plot...")
#         self.plot_correlation_matrix(sensor_data, health_index, save_path)
        
#         # 如果有3D坐标，生成3D可视化
#         if node_coords.shape[1] > 2:
#             print("Generating 3D scatter visualization...")
#             self.plot_3d_visualization(node_coords, health_index, 
#                                      'Pearl Mussel Health Index 3D Distribution', 
#                                      self.color_maps['health'], save_path)
        
#         print("✅ All visualization plots generated successfully!")
    
#     def plot_time_series(self, time_data, health_data, title="Health Index Time Series", save_path=None):
#         """
#         Plot time series
        
#         Args:
#             time_data (np.array): Time data
#             health_data (np.array): Health data
#             title (str): Figure title
#             save_path (str): Save path
#         """
#         fig, ax = plt.subplots(figsize=self.figsize)
        
#         ax.plot(time_data, health_data, linewidth=2, color='blue')
#         ax.set_xlabel('Time (days)')
#         ax.set_ylabel('Health Index')
#         ax.set_title(title)
#         ax.grid(True, alpha=0.3)
        
#         plt.tight_layout()
        
#         if save_path:
#             self.create_directory(save_path)
#             plt.savefig(f'{save_path}/time_series.png', 
#                        dpi=self.dpi, bbox_inches='tight')
#         plt.show()






# dynamic_mathematical_model/visualization.py

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.tri as mtri
import seaborn as sns
import pandas as pd
import os

# 设置全局样式
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

class ModelVisualizer:
    """负责所有与模型相关的可视化任务"""
    
    def __init__(self, dpi=150):
        self.dpi = dpi
        self.color_maps = {
            'temperature': 'coolwarm',
            'ph': 'RdYlBu_r',
            'conductivity': 'viridis',
            'chlorophyll': 'Greens',
            'health': 'RdYlGn' # 用于健康度
        }

    def _create_directory(self, path):
        """如果目录不存在则创建"""
        if not os.path.exists(path):
            os.makedirs(path)

    def _get_prefix(self, is_prediction):
        """根据模式返回文件名前缀"""
        return "prediction_" if is_prediction else ""

    def plot_sensor_data_2d(self, node_coords, sensor_data, elements, save_path, is_prediction):
        """绘制2D传感器数据热力图"""
        prefix = self._get_prefix(is_prediction)
        triang = mtri.Triangulation(node_coords[:, 0], node_coords[:, 1], elements[:, :3] - 1)
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        parameters = list(sensor_data.keys())
        titles = [f'{p.capitalize()} Distribution' for p in parameters]

        for i, (param, title) in enumerate(zip(parameters, titles)):
            ax = axes[i//2, i%2]
            tcf = ax.tricontourf(triang, sensor_data[param], levels=100, cmap=self.color_maps[param])
            ax.set_xlabel('X Coordinate (m)'); ax.set_ylabel('Y Coordinate (m)')
            ax.set_title(title); ax.set_aspect('equal')
            cbar = plt.colorbar(tcf, ax=ax); cbar.set_label(param.capitalize())

        plt.tight_layout()
        plt.savefig(os.path.join(save_path, f'{prefix}sensor_data_2d.png'), dpi=self.dpi)
        plt.close()

    def plot_health_index_2d(self, node_coords, health_index, elements, save_path, is_prediction):
        """绘制2D健康度指数热力图"""
        prefix = self._get_prefix(is_prediction)
        triang = mtri.Triangulation(node_coords[:, 0], node_coords[:, 1], elements[:, :3] - 1)
        
        plt.figure(figsize=(8, 6))
        tcf = plt.tricontourf(triang, health_index, levels=150, cmap=self.color_maps['health'], vmin=0, vmax=1)
        plt.xlabel('X Coordinate (m)'); plt.ylabel('Y Coordinate (m)')
        plt.title('Pearl Mussel Health Index Distribution')
        plt.gca().set_aspect('equal')
        cbar = plt.colorbar(tcf); cbar.set_label('Health Index (u)')

        plt.tight_layout()
        plt.savefig(os.path.join(save_path, f'{prefix}health_index_2d.png'), dpi=self.dpi)
        plt.close()

    def plot_3d_visualization(self, node_coords, health_index, save_path, is_prediction):
        """创建3D健康度散点图"""
        prefix = self._get_prefix(is_prediction)
        fig = plt.figure(figsize=(12, 9))
        ax = fig.add_subplot(111, projection='3d')
        
        scatter = ax.scatter(node_coords[:, 0], node_coords[:, 1], node_coords[:, 2],
                           c=health_index, cmap=self.color_maps['health'], s=20, alpha=0.7, vmin=0, vmax=1)
        
        ax.set_xlabel('X (m)'); ax.set_ylabel('Y (m)'); ax.set_zlabel('Z (m)')
        ax.set_title('3D Health Index Distribution')
        cbar = plt.colorbar(scatter, ax=ax, shrink=0.6); cbar.set_label('Health Index (u)')
        
        plt.savefig(os.path.join(save_path, f'{prefix}health_index_3d.png'), dpi=self.dpi)
        plt.close()

    def plot_correlation_matrix(self, sensor_data, health_index, save_path, is_prediction):
        """绘制参数相关性矩阵热力图"""
        prefix = self._get_prefix(is_prediction)
        df_data = sensor_data.copy()
        df_data['Health Index'] = health_index
        df = pd.DataFrame(df_data)
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(df.corr(), annot=True, cmap='coolwarm', center=0, square=True)
        plt.title('Correlation Matrix of Parameters and Health Index')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, f'{prefix}correlation_matrix.png'), dpi=self.dpi)
        plt.close()

    def plot_parameter_distributions(self, sensor_data, health_index, save_path, is_prediction):
        """绘制参数分布直方图"""
        prefix = self._get_prefix(is_prediction)
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        all_data = sensor_data.copy()
        all_data['Health Index'] = health_index
        
        for i, (param, data) in enumerate(all_data.items()):
            ax = axes.flatten()[i]
            sns.histplot(data, bins=30, ax=ax, kde=True)
            ax.set_title(f'{param.capitalize()} Distribution')
            ax.axvline(np.mean(data), color='red', linestyle='--', label=f'Mean: {np.mean(data):.2f}')
            ax.legend()
        
        # 隐藏多余的子图（如果有的话）
        for j in range(len(all_data), len(axes.flatten())):
            axes.flatten()[j].set_visible(False)
            
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, f'{prefix}parameter_distributions.png'), dpi=self.dpi)
        plt.close()

    def create_comprehensive_visualization(self, node_coords, sensor_data, health_index=None, elements=None, save_path='results', is_prediction=False):
        """
        创建一套完整的可视化图表。
        会智能检查传入的数据来决定绘制哪些图。
        """
        self._create_directory(save_path)

        # --- 智能检查逻辑 ---

        # 只有当sensor_data字典不为空时，才绘制传感器数据图
        if sensor_data:
            print("  > Generating 2D sensor data plots...")
            self.plot_sensor_data_2d(node_coords, sensor_data, elements, save_path, is_prediction)

        # 只有当health_index被传入时，才绘制健康度图
        if health_index is not None:
            print("  > Generating health index plots (2D and 3D)...")
            self.plot_health_index_2d(node_coords, health_index, elements, save_path, is_prediction)
            if node_coords.shape[1] > 2: # 检查是否有Z坐标
                self.plot_3d_visualization(node_coords, health_index, save_path, is_prediction)

        # 只有当两者都存在时，才能进行综合分析
        if sensor_data and health_index is not None:
            print("  > Generating comprehensive analysis plots (Distributions, Correlation)...")
            self.plot_parameter_distributions(sensor_data, health_index, save_path, is_prediction)
            self.plot_correlation_matrix(sensor_data, health_index, save_path, is_prediction)

        print(f"✅ Visualization charts saved to '{save_path}'")


class PearlFarmingVisualizer:
    """Pearl farming system specific visualizer for pond layout and robot navigation"""

    def __init__(self, pond_length=50.0, pond_width=30.0, dpi=150):
        """
        Initialize the pearl farming visualizer

        Args:
            pond_length (float): Length of the pond in meters (50m)
            pond_width (float): Width of the pond in meters (30m)
            dpi (int): Figure resolution
        """
        self.pond_length = pond_length
        self.pond_width = pond_width
        self.dpi = dpi

        # Pearl layout parameters
        self.pearl_spacing = 1.0  # 1m spacing between pearls within rows
        self.row_spacing = 1.5    # 1.5m spacing between rows

        # Calculate grid parameters
        self._calculate_grid_parameters()

    def _calculate_grid_parameters(self):
        """Calculate grid density and pearl positions"""
        # Number of rows along the width (30m direction)
        self.num_rows = int(self.pond_width / self.row_spacing)

        # Number of pearls per row along the length (50m direction)
        self.pearls_per_row = int(self.pond_length / self.pearl_spacing)

        # Adjust spacing to fit exactly within pond dimensions
        self.actual_row_spacing = self.pond_width / self.num_rows
        self.actual_pearl_spacing = self.pond_length / self.pearls_per_row

        print(f"Grid Parameters:")
        print(f"  - Number of rows: {self.num_rows}")
        print(f"  - Pearls per row: {self.pearls_per_row}")
        print(f"  - Actual row spacing: {self.actual_row_spacing:.2f}m")
        print(f"  - Actual pearl spacing: {self.actual_pearl_spacing:.2f}m")

    def generate_pearl_positions(self):
        """
        Generate pearl positions based on the grid system

        Returns:
            np.array: Pearl positions (N, 2) with x, y coordinates
        """
        pearl_positions = []

        for row_idx in range(self.num_rows):
            # Y position for this row (along width)
            y_pos = (row_idx + 0.5) * self.actual_row_spacing

            for pearl_idx in range(self.pearls_per_row):
                # X position for this pearl (along length)
                x_pos = (pearl_idx + 0.5) * self.actual_pearl_spacing
                pearl_positions.append([x_pos, y_pos])

        return np.array(pearl_positions)

    def generate_ladder_pattern_path(self):
        """
        Generate ladder-pattern robot navigation path

        Returns:
            dict: Dictionary containing different path segments:
                - 'vertical_segments': List of vertical column paths (ladder rungs)
                - 'horizontal_connections': List of horizontal connection paths (ladder sides)
                - 'all_points': Combined path points for visualization
        """
        vertical_segments = []
        horizontal_connections = []
        all_points = []

        # Generate vertical segments (ladder rungs) - column-wise movement
        for col_idx in range(self.pearls_per_row):
            x_pos = (col_idx + 0.5) * self.actual_pearl_spacing

            # Create vertical segment from bottom to top
            y_positions = np.linspace(0, self.pond_width, 50)
            vertical_segment = []

            for y_pos in y_positions:
                point = [x_pos, y_pos]
                vertical_segment.append(point)
                all_points.append(point)

            vertical_segments.append(np.array(vertical_segment))

        # Generate horizontal connections (ladder sides) at pond edges
        for col_idx in range(self.pearls_per_row - 1):
            current_x = (col_idx + 0.5) * self.actual_pearl_spacing
            next_x = (col_idx + 1.5) * self.actual_pearl_spacing

            # Bottom connection (y = 0)
            bottom_connection = []
            x_positions = np.linspace(current_x, next_x, 20)
            for x_pos in x_positions:
                point = [x_pos, 0]
                bottom_connection.append(point)
                all_points.append(point)
            horizontal_connections.append(np.array(bottom_connection))

            # Top connection (y = pond_width)
            top_connection = []
            for x_pos in x_positions:
                point = [x_pos, self.pond_width]
                top_connection.append(point)
                all_points.append(point)
            horizontal_connections.append(np.array(top_connection))

        return {
            'vertical_segments': vertical_segments,
            'horizontal_connections': horizontal_connections,
            'all_points': np.array(all_points)
        }

    def _create_directory(self, path):
        """Create directory if it doesn't exist"""
        if not os.path.exists(path):
            os.makedirs(path)

    def plot_pond_layout_with_grid(self, save_path='results'):
        """
        Plot the pond layout showing pearl positions and grid system

        Args:
            save_path (str): Directory to save the plot
        """
        self._create_directory(save_path)

        # Generate pearl positions
        pearl_positions = self.generate_pearl_positions()

        # Create the plot
        fig, ax = plt.subplots(figsize=(15, 9))

        # Plot pond boundary
        pond_rect = plt.Rectangle((0, 0), self.pond_length, self.pond_width,
                                 fill=False, edgecolor='black', linewidth=3,
                                 label='Pond Boundary')
        ax.add_patch(pond_rect)

        # Plot pearl positions
        ax.scatter(pearl_positions[:, 0], pearl_positions[:, 1],
                  c='darkblue', s=30, alpha=0.7, label='Pearl Positions')

        # Draw row separation lines (parallel to long edge)
        for row_idx in range(self.num_rows + 1):
            y_pos = row_idx * self.actual_row_spacing
            ax.axhline(y=y_pos, color='red', linestyle='--', alpha=0.6, linewidth=1)

        # Draw column grid lines (parallel to short edge)
        for col_idx in range(self.pearls_per_row + 1):
            x_pos = col_idx * self.actual_pearl_spacing
            ax.axvline(x=x_pos, color='gray', linestyle=':', alpha=0.4, linewidth=0.5)

        # Add labels and formatting
        ax.set_xlabel('Length (m) - 50m Direction', fontsize=12)
        ax.set_ylabel('Width (m) - 30m Direction', fontsize=12)
        ax.set_title('Pearl Farming System Layout\nPond Dimensions: 50m × 30m', fontsize=14, fontweight='bold')
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.legend(loc='upper right')

        # Add annotations
        ax.text(self.pond_length/2, -2, f'Pearl Spacing: {self.actual_pearl_spacing:.1f}m',
                ha='center', fontsize=10, bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))
        ax.text(-3, self.pond_width/2, f'Row Spacing: {self.actual_row_spacing:.1f}m',
                ha='center', rotation=90, fontsize=10, bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7))

        # Set axis limits with some padding
        ax.set_xlim(-5, self.pond_length + 5)
        ax.set_ylim(-5, self.pond_width + 5)

        plt.tight_layout()
        plt.savefig(os.path.join(save_path, 'pond_layout_with_grid.png'), dpi=self.dpi, bbox_inches='tight')
        plt.close()

    def plot_ladder_pattern_navigation(self, save_path='results'):
        """
        Plot the ladder-pattern robot navigation path

        Args:
            save_path (str): Directory to save the plot
        """
        self._create_directory(save_path)

        # Generate pearl positions and navigation path
        pearl_positions = self.generate_pearl_positions()
        ladder_paths = self.generate_ladder_pattern_path()

        # Create the plot
        fig, ax = plt.subplots(figsize=(16, 10))

        # Plot pond boundary
        pond_rect = plt.Rectangle((0, 0), self.pond_length, self.pond_width,
                                 fill=False, edgecolor='black', linewidth=3,
                                 label='Pond Boundary')
        ax.add_patch(pond_rect)

        # Plot pearl positions (lighter and smaller for context)
        ax.scatter(pearl_positions[:, 0], pearl_positions[:, 1],
                  c='lightblue', s=15, alpha=0.4, label='Pearl Positions', zorder=1)

        # Plot vertical segments (ladder rungs) - scanning routes
        for i, vertical_segment in enumerate(ladder_paths['vertical_segments']):
            if i == 0:  # Add label only for the first segment
                ax.plot(vertical_segment[:, 0], vertical_segment[:, 1],
                       color='red', linewidth=3, alpha=0.8,
                       label='Vertical Scanning Routes (Ladder Rungs)', zorder=3)
            else:
                ax.plot(vertical_segment[:, 0], vertical_segment[:, 1],
                       color='red', linewidth=3, alpha=0.8, zorder=3)

        # Plot horizontal connections (ladder sides) - connection routes
        for i, horizontal_connection in enumerate(ladder_paths['horizontal_connections']):
            if i == 0:  # Add label only for the first connection
                ax.plot(horizontal_connection[:, 0], horizontal_connection[:, 1],
                       color='blue', linewidth=2.5, alpha=0.7, linestyle='--',
                       label='Horizontal Connection Routes (Ladder Sides)', zorder=2)
            else:
                ax.plot(horizontal_connection[:, 0], horizontal_connection[:, 1],
                       color='blue', linewidth=2.5, alpha=0.7, linestyle='--', zorder=2)

        # Draw row separation lines (navigation corridors)
        for row_idx in range(self.num_rows + 1):
            y_pos = row_idx * self.actual_row_spacing
            ax.axhline(y=y_pos, color='gray', linestyle=':', alpha=0.5, linewidth=1)

        # Add connection zone indicators
        self._add_connection_zone_indicators(ax)

        # Add labels and formatting
        ax.set_xlabel('Length (m) - 50m Direction', fontsize=12)
        ax.set_ylabel('Width (m) - 30m Direction', fontsize=12)
        ax.set_title('Robot Ladder-Pattern Navigation Network\nVertical Scanning + Horizontal Connections',
                    fontsize=14, fontweight='bold')
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.legend(loc='upper right', fontsize=10)

        # Set axis limits with some padding
        ax.set_xlim(-3, self.pond_length + 3)
        ax.set_ylim(-3, self.pond_width + 3)

        # Add annotations
        ax.text(self.pond_length/2, -2, 'Ladder Pattern: Vertical scanning columns connected by horizontal routes at pond edges',
                ha='center', fontsize=10, style='italic',
                bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))

        plt.tight_layout()
        plt.savefig(os.path.join(save_path, 'ladder_pattern_navigation.png'), dpi=self.dpi, bbox_inches='tight')
        plt.close()

    def _add_connection_zone_indicators(self, ax):
        """Add visual indicators for connection zones at pond edges"""
        # Bottom connection zone
        bottom_zone = plt.Rectangle((0, -1), self.pond_length, 1,
                                   fill=True, facecolor='lightblue', alpha=0.3,
                                   edgecolor='blue', linewidth=1)
        ax.add_patch(bottom_zone)
        ax.text(self.pond_length/2, -0.5, 'Bottom Connection Zone',
               ha='center', va='center', fontsize=9, fontweight='bold', color='blue')

        # Top connection zone
        top_zone = plt.Rectangle((0, self.pond_width), self.pond_length, 1,
                                fill=True, facecolor='lightcoral', alpha=0.3,
                                edgecolor='red', linewidth=1)
        ax.add_patch(top_zone)
        ax.text(self.pond_length/2, self.pond_width + 0.5, 'Top Connection Zone',
               ha='center', va='center', fontsize=9, fontweight='bold', color='red')

    def _add_direction_arrows(self, ax, path_points, arrow_interval=200):
        """Add direction arrows to show robot movement direction"""
        for i in range(0, len(path_points) - arrow_interval, arrow_interval):
            start_point = path_points[i]
            end_point = path_points[i + arrow_interval//4]  # Shorter arrows

            dx = end_point[0] - start_point[0]
            dy = end_point[1] - start_point[1]

            ax.arrow(start_point[0], start_point[1], dx, dy,
                    head_width=0.8, head_length=0.5, fc='red', ec='red', alpha=0.7)

    def plot_comprehensive_pond_system(self, save_path='results', include_water_flow=True):
        """
        Create a comprehensive visualization showing all system components

        Args:
            save_path (str): Directory to save the plot
            include_water_flow (bool): Whether to include water flow indicators
        """
        self._create_directory(save_path)

        # Generate data
        pearl_positions = self.generate_pearl_positions()
        path_points = self.generate_s_pattern_path()

        # Create figure with subplots
        fig = plt.figure(figsize=(20, 12))

        # Main comprehensive plot
        ax1 = plt.subplot(2, 2, (1, 2))  # Top row, spanning 2 columns

        # Plot pond boundary
        pond_rect = plt.Rectangle((0, 0), self.pond_length, self.pond_width,
                                 fill=False, edgecolor='black', linewidth=3,
                                 label='Pond Boundary')
        ax1.add_patch(pond_rect)

        # Plot pearl positions
        ax1.scatter(pearl_positions[:, 0], pearl_positions[:, 1],
                   c='darkblue', s=40, alpha=0.7, label='Pearl Positions', zorder=2)

        # Plot S-pattern path
        ax1.plot(path_points[:, 0], path_points[:, 1],
                color='red', linewidth=2.5, alpha=0.8, label='Robot S-Pattern Path', zorder=3)

        # Draw row separation lines (mandatory navigation corridors)
        for row_idx in range(self.num_rows + 1):
            y_pos = row_idx * self.actual_row_spacing
            ax1.axhline(y=y_pos, color='red', linestyle='-', alpha=0.8, linewidth=2,
                       label='Row Separation Lines' if row_idx == 0 else "")

        # Draw column grid lines
        for col_idx in range(self.pearls_per_row + 1):
            x_pos = col_idx * self.actual_pearl_spacing
            ax1.axvline(x=x_pos, color='gray', linestyle=':', alpha=0.5, linewidth=1)

        # Mark start and end points
        ax1.scatter(path_points[0, 0], path_points[0, 1],
                   c='green', s=150, marker='o', label='Start Point', zorder=4, edgecolor='black')
        ax1.scatter(path_points[-1, 0], path_points[-1, 1],
                   c='orange', s=150, marker='s', label='End Point', zorder=4, edgecolor='black')

        # Add water flow indicators if requested
        if include_water_flow:
            self._add_water_flow_indicators(ax1)

        # Add movement direction arrows
        self._add_direction_arrows(ax1, path_points, arrow_interval=150)

        # Formatting for main plot
        ax1.set_xlabel('Length (m) - 50m Direction', fontsize=12)
        ax1.set_ylabel('Width (m) - 30m Direction', fontsize=12)
        ax1.set_title('Comprehensive Pearl Farming System Layout\nwith Robot Navigation Strategy',
                     fontsize=16, fontweight='bold')
        ax1.set_aspect('equal')
        ax1.grid(True, alpha=0.3)
        ax1.legend(loc='upper right', fontsize=10)
        ax1.set_xlim(-3, self.pond_length + 3)
        ax1.set_ylim(-3, self.pond_width + 3)

        # Subplot 1: Grid density analysis
        ax2 = plt.subplot(2, 2, 3)
        self._plot_grid_density_analysis(ax2)

        # Subplot 2: Navigation constraints
        ax3 = plt.subplot(2, 2, 4)
        self._plot_navigation_constraints(ax3)

        plt.tight_layout()
        plt.savefig(os.path.join(save_path, 'comprehensive_pond_system.png'),
                   dpi=self.dpi, bbox_inches='tight')
        plt.close()

    def _add_water_flow_indicators(self, ax):
        """Add water flow direction indicators to the plot"""
        # Inlet at one end (left side)
        inlet_rect = plt.Rectangle((-2, self.pond_width/2 - 2), 1.5, 4,
                                  fill=True, facecolor='lightblue', edgecolor='blue',
                                  linewidth=2, alpha=0.7, label='Water Inlet')
        ax.add_patch(inlet_rect)
        ax.text(-1.25, self.pond_width/2, 'INLET', ha='center', va='center',
               fontweight='bold', fontsize=8, rotation=90)

        # Outlet at the other end (right side)
        outlet_rect = plt.Rectangle((self.pond_length + 0.5, self.pond_width/2 - 2), 1.5, 4,
                                   fill=True, facecolor='lightcoral', edgecolor='red',
                                   linewidth=2, alpha=0.7, label='Water Outlet')
        ax.add_patch(outlet_rect)
        ax.text(self.pond_length + 1.25, self.pond_width/2, 'OUTLET', ha='center', va='center',
               fontweight='bold', fontsize=8, rotation=90)

        # Flow direction arrows
        for y in np.linspace(5, self.pond_width-5, 5):
            for x in np.linspace(5, self.pond_length-5, 8):
                ax.arrow(x, y, 2, 0, head_width=0.5, head_length=1,
                        fc='cyan', ec='cyan', alpha=0.4, linewidth=0.5)

    def _plot_grid_density_analysis(self, ax):
        """Plot grid density and node distribution analysis"""
        # Calculate grid statistics
        total_pearls = self.num_rows * self.pearls_per_row
        pearl_density = total_pearls / (self.pond_length * self.pond_width)

        # Create bar chart of grid parameters
        categories = ['Rows', 'Pearls/Row', 'Total Pearls']
        values = [self.num_rows, self.pearls_per_row, total_pearls]
        colors = ['lightblue', 'lightgreen', 'lightcoral']

        bars = ax.bar(categories, values, color=colors, alpha=0.7, edgecolor='black')

        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
                   f'{value}', ha='center', va='bottom', fontweight='bold')

        ax.set_title('Grid Density Analysis', fontweight='bold')
        ax.set_ylabel('Count')
        ax.grid(True, alpha=0.3)

        # Add density information
        ax.text(0.5, 0.95, f'Pearl Density: {pearl_density:.2f} pearls/m²',
               transform=ax.transAxes, ha='center', va='top',
               bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7))

    def _plot_navigation_constraints(self, ax):
        """Plot navigation constraints and movement rules"""
        # Create a simplified schematic
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 6)

        # Draw simplified pond
        pond_rect = plt.Rectangle((1, 1), 8, 4, fill=False, edgecolor='black', linewidth=2)
        ax.add_patch(pond_rect)

        # Draw columns (allowed movement)
        for i in range(3):
            x = 2 + i * 2.5
            ax.arrow(x, 1.5, 0, 3, head_width=0.2, head_length=0.2,
                    fc='green', ec='green', linewidth=2, alpha=0.7)
            ax.text(x, 0.8, f'Col {i+1}', ha='center', fontsize=8)

        # Draw prohibited cross-row movement
        ax.plot([2, 4.5], [3, 3], 'r-', linewidth=3, alpha=0.7)
        ax.plot([2.2, 2.2], [2.8, 3.2], 'r-', linewidth=3)
        ax.plot([4.3, 4.3], [2.8, 3.2], 'r-', linewidth=3)
        ax.text(3.25, 3.3, 'PROHIBITED', ha='center', color='red', fontweight='bold', fontsize=8)

        # Add transition zones
        ax.arrow(2, 5.2, 2.5, 0, head_width=0.15, head_length=0.2,
                fc='blue', ec='blue', linewidth=2, alpha=0.7)
        ax.text(3.25, 5.5, 'Transition Zone', ha='center', color='blue', fontweight='bold', fontsize=8)

        ax.set_title('Navigation Constraints', fontweight='bold')
        ax.set_xticks([])
        ax.set_yticks([])
        ax.text(5, 0.3, 'Robot Movement Rules:\n• Column-wise scanning only\n• No direct cross-row movement\n• Transitions at pond edges',
               ha='center', va='bottom', fontsize=8,
               bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.7))

    def create_ladder_pattern_visualization(self, save_path='results'):
        """
        Create ladder-pattern navigation visualization

        Args:
            save_path (str): Directory to save the plot
        """
        self._create_directory(save_path)

        print("🎯 Generating Ladder-Pattern Navigation Visualization...")
        print("=" * 60)

        print("🪜 Creating ladder-pattern navigation network...")
        self.plot_ladder_pattern_navigation(save_path)

        print("=" * 60)
        print(f"✅ Ladder-pattern navigation visualization saved to: {save_path}")
        print("\nGenerated file:")
        print("  - ladder_pattern_navigation.png")

        # Print summary statistics
        pearl_positions = self.generate_pearl_positions()
        ladder_paths = self.generate_ladder_pattern_path()

        print(f"\n📈 Ladder Pattern Statistics:")
        print(f"  - Total pearl positions: {len(pearl_positions)}")
        print(f"  - Vertical segments (ladder rungs): {len(ladder_paths['vertical_segments'])}")
        print(f"  - Horizontal connections (ladder sides): {len(ladder_paths['horizontal_connections'])}")
        print(f"  - Total navigation points: {len(ladder_paths['all_points'])}")
        print(f"  - Pearl density: {len(pearl_positions)/(self.pond_length*self.pond_width):.2f} pearls/m²")

        # Calculate ladder pattern statistics
        total_vertical_length = len(ladder_paths['vertical_segments']) * self.pond_width
        total_horizontal_length = len(ladder_paths['horizontal_connections']) * self.actual_pearl_spacing
        total_network_length = total_vertical_length + total_horizontal_length

        print(f"\n🪜 Network Analysis:")
        print(f"  - Total vertical scanning length: {total_vertical_length:.1f}m")
        print(f"  - Total horizontal connection length: {total_horizontal_length:.1f}m")
        print(f"  - Total network length: {total_network_length:.1f}m")
        print(f"  - Network density: {total_network_length/(self.pond_length*self.pond_width):.2f}m/m²")

    def _calculate_path_length(self, path_points):
        """Calculate total length of the navigation path"""
        total_length = 0
        for i in range(1, len(path_points)):
            dx = path_points[i, 0] - path_points[i-1, 0]
            dy = path_points[i, 1] - path_points[i-1, 1]
            total_length += np.sqrt(dx**2 + dy**2)
        return total_length


# Demonstration function
def demonstrate_ladder_pattern_navigation():
    """
    Demonstration function to showcase the ladder-pattern navigation visualization
    """
    print("🪜 Ladder-Pattern Navigation Visualization Demo")
    print("=" * 50)

    # Create visualizer instance
    visualizer = PearlFarmingVisualizer(pond_length=50.0, pond_width=30.0)

    # Generate ladder pattern visualization
    visualizer.create_ladder_pattern_visualization(save_path='results/ladder_pattern_navigation')

    print("\n🎉 Ladder-pattern demo completed successfully!")
    print("Check the 'results/ladder_pattern_navigation' directory for the generated visualization.")


if __name__ == "__main__":
    # Run demonstration when script is executed directly
    demonstrate_ladder_pattern_navigation()