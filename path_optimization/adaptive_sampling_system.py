#!/usr/bin/env python3
"""
智能自适应采样与路径优化系统
Intelligent Adaptive Sampling and Path Optimization System

This module implements the mathematical model for intelligent adaptive sampling
with real-time data collection, GGNN analysis, and multi-objective path optimization.

Key Features:
1. Sparse sampling strategy based on betweenness centrality
2. Multi-depth sampling at critical nodes
3. IDW interpolation for unsampled nodes
4. Gated Graph Neural Network (GGNN) analysis
5. Adaptive secondary sampling
6. Multi-objective path optimization

Author: Pearl Farming Monitoring System
Date: 2025-01-26
"""

import numpy as np
import networkx as nx
from scipy.spatial.distance import cdist
from scipy.interpolate import griddata
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Set, Optional
import matplotlib.pyplot as plt
from dataclasses import dataclass
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SamplingConfig:
    """Configuration parameters for adaptive sampling system"""
    health_threshold: float = 0.6
    gradient_threshold: float = 0.1
    max_sampling_capacity: int = 100
    max_time_budget: float = 3600.0  # seconds
    depth_levels: List[float] = None
    idw_power: float = 2.0
    neighbor_radius: float = 5.0
    
    def __post_init__(self):
        if self.depth_levels is None:
            self.depth_levels = [0.5, 1.5, 2.5]  # surface, middle, bottom

class BetweennessCentralitySampler:
    """
    稀疏采样策略设计 - 基于介数中心性算法
    Sparse sampling strategy based on betweenness centrality
    """
    
    def __init__(self, graph: nx.Graph, pearl_positions: List[int]):
        self.graph = graph
        self.pearl_positions = set(pearl_positions)
        self.centrality_scores = None
        
    def compute_betweenness_centrality(self) -> Dict[int, float]:
        """
        计算所有节点的介数中心性
        Compute betweenness centrality for all nodes
        """
        logger.info("Computing betweenness centrality...")
        self.centrality_scores = nx.betweenness_centrality(self.graph)
        return self.centrality_scores
    
    def select_initial_sampling_points(self, percentile: float = 80.0) -> Set[int]:
        """
        选择初始采样点：高中心性节点 + 珍珠位置
        Select initial sampling points: high centrality nodes + pearl positions
        """
        if self.centrality_scores is None:
            self.compute_betweenness_centrality()
        
        # Calculate threshold based on percentile
        threshold = np.percentile(list(self.centrality_scores.values()), percentile)
        
        # Select high centrality nodes
        high_centrality_nodes = {
            node for node, score in self.centrality_scores.items() 
            if score >= threshold
        }
        
        # Combine with pearl positions
        initial_points = high_centrality_nodes.union(self.pearl_positions)
        
        logger.info(f"Selected {len(initial_points)} initial sampling points")
        logger.info(f"High centrality nodes: {len(high_centrality_nodes)}")
        logger.info(f"Pearl positions: {len(self.pearl_positions)}")
        
        return initial_points

class MultiDepthSampler:
    """
    多深度采样决策器
    Multi-depth sampling decision maker
    """
    
    def __init__(self, config: SamplingConfig):
        self.config = config
        
    def decide_sampling_depths(self, node: int, centrality_score: float, 
                             is_pearl_position: bool = False) -> List[float]:
        """
        决定节点的采样深度
        Decide sampling depths for a node
        """
        depths = []
        
        # Always sample surface, middle, bottom for critical nodes
        if is_pearl_position or centrality_score >= 0.1:
            depths = self.config.depth_levels.copy()
        else:
            # For regular nodes, sample fewer depths
            depths = [self.config.depth_levels[0], self.config.depth_levels[-1]]
        
        return depths

class IDWInterpolator:
    """
    反距离权重插值器
    Inverse Distance Weighting (IDW) Interpolator
    """
    
    def __init__(self, power: float = 2.0, epsilon: float = 1e-6):
        self.power = power
        self.epsilon = epsilon
        
    def interpolate(self, sampled_positions: np.ndarray, sampled_values: np.ndarray,
                   target_positions: np.ndarray) -> np.ndarray:
        """
        使用IDW方法插值未采样节点的环境参数
        Interpolate environmental parameters for unsampled nodes using IDW
        
        Args:
            sampled_positions: (n_sampled, 2) array of sampled node coordinates
            sampled_values: (n_sampled, n_features) array of environmental values
            target_positions: (n_targets, 2) array of target node coordinates
            
        Returns:
            interpolated_values: (n_targets, n_features) array of interpolated values
        """
        # Calculate distances
        distances = cdist(target_positions, sampled_positions)
        
        # Calculate weights (avoid division by zero)
        weights = 1.0 / (distances ** self.power + self.epsilon)
        
        # Normalize weights
        weights = weights / weights.sum(axis=1, keepdims=True)
        
        # Interpolate values
        interpolated_values = np.dot(weights, sampled_values)
        
        return interpolated_values

class HealthIndexCalculator:
    """
    生物数学健康度计算器
    Biological mathematical health index calculator
    """
    
    def __init__(self):
        # Optimal parameters for each environmental factor
        self.optimal_params = {
            'temperature': {'mu': 25.0, 'sigma': 3.0, 'weight': 0.3},
            'ph': {'mu': 7.5, 'sigma': 0.5, 'weight': 0.25},
            'conductivity': {'mu': 500.0, 'sigma': 100.0, 'weight': 0.25},
            'chlorophyll': {'mu': 10.0, 'sigma': 5.0, 'weight': 0.2}
        }
        
    def calculate_health_index(self, environmental_data: np.ndarray) -> np.ndarray:
        """
        基于环境参数计算健康度指数
        Calculate health index based on environmental parameters
        
        Args:
            environmental_data: (n_nodes, 4) array [temperature, pH, conductivity, chlorophyll]
            
        Returns:
            health_indices: (n_nodes,) array of health indices [0, 1]
        """
        n_nodes = environmental_data.shape[0]
        health_indices = np.ones(n_nodes)
        
        factor_names = ['temperature', 'ph', 'conductivity', 'chlorophyll']
        
        for i, factor_name in enumerate(factor_names):
            params = self.optimal_params[factor_name]
            values = environmental_data[:, i]
            
            # Calculate Gaussian fitness for each factor
            fitness = np.exp(-((values - params['mu']) ** 2) / (2 * params['sigma'] ** 2))
            
            # Apply weight and multiply to overall health
            health_indices *= fitness ** params['weight']
        
        return health_indices

class GatedGraphNeuralNetwork(nn.Module):
    """
    门控图神经网络 (GGNN) 用于因果分析
    Gated Graph Neural Network for causal analysis
    """
    
    def __init__(self, input_dim: int, hidden_dim: int, output_dim: int, 
                 num_layers: int = 3, use_attention: bool = True):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        self.use_attention = use_attention
        
        # Input projection
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        
        # Message passing layers
        self.message_layers = nn.ModuleList([
            nn.Linear(hidden_dim, hidden_dim) for _ in range(num_layers)
        ])
        
        # Gated update components
        self.update_gate = nn.Linear(2 * hidden_dim, hidden_dim)
        self.reset_gate = nn.Linear(2 * hidden_dim, hidden_dim)
        self.new_gate = nn.Linear(2 * hidden_dim, hidden_dim)
        
        # Attention mechanism (optional)
        if use_attention:
            self.attention = nn.Linear(2 * hidden_dim, 1)
        
        # Output projection
        self.output_projection = nn.Linear(hidden_dim, output_dim)
        
    def forward(self, node_features: torch.Tensor, adjacency_matrix: torch.Tensor) -> torch.Tensor:
        """
        GGNN前向传播
        GGNN forward pass
        
        Args:
            node_features: (batch_size, num_nodes, input_dim)
            adjacency_matrix: (batch_size, num_nodes, num_nodes)
            
        Returns:
            output: (batch_size, num_nodes, output_dim)
        """
        batch_size, num_nodes, _ = node_features.shape
        
        # Initial hidden states
        h = self.input_projection(node_features)  # (batch_size, num_nodes, hidden_dim)
        
        # Message passing iterations
        for layer_idx in range(self.num_layers):
            # Aggregate messages from neighbors
            if self.use_attention:
                # Attention-based message aggregation
                messages = self._attention_aggregation(h, adjacency_matrix)
            else:
                # Simple message aggregation
                messages = torch.bmm(adjacency_matrix, h)  # (batch_size, num_nodes, hidden_dim)
            
            # Apply message transformation
            messages = self.message_layers[layer_idx](messages)
            
            # Gated update
            h = self._gated_update(h, messages)
        
        # Output projection
        output = self.output_projection(h)
        
        return output
    
    def _attention_aggregation(self, h: torch.Tensor, adjacency_matrix: torch.Tensor) -> torch.Tensor:
        """
        注意力机制消息聚合
        Attention-based message aggregation
        """
        batch_size, num_nodes, hidden_dim = h.shape
        
        # Compute attention scores
        h_expanded = h.unsqueeze(2).expand(-1, -1, num_nodes, -1)  # (batch, nodes, nodes, hidden)
        h_transposed = h.unsqueeze(1).expand(-1, num_nodes, -1, -1)  # (batch, nodes, nodes, hidden)
        
        attention_input = torch.cat([h_expanded, h_transposed], dim=-1)  # (batch, nodes, nodes, 2*hidden)
        attention_scores = self.attention(attention_input).squeeze(-1)  # (batch, nodes, nodes)
        
        # Apply adjacency mask and softmax
        attention_scores = attention_scores.masked_fill(adjacency_matrix == 0, float('-inf'))
        attention_weights = F.softmax(attention_scores, dim=-1)
        
        # Aggregate messages
        messages = torch.bmm(attention_weights, h)
        
        return messages
    
    def _gated_update(self, h: torch.Tensor, messages: torch.Tensor) -> torch.Tensor:
        """
        门控更新机制
        Gated update mechanism
        """
        combined = torch.cat([h, messages], dim=-1)
        
        # Compute gates
        update_gate = torch.sigmoid(self.update_gate(combined))
        reset_gate = torch.sigmoid(self.reset_gate(combined))
        
        # Compute new candidate
        reset_h = reset_gate * h
        new_combined = torch.cat([reset_h, messages], dim=-1)
        new_h = torch.tanh(self.new_gate(new_combined))
        
        # Update hidden state
        h_new = (1 - update_gate) * h + update_gate * new_h
        
        return h_new

class GNNExplainer:
    """
    GNN解释器用于特征重要性分析
    GNN Explainer for feature importance analysis
    """
    
    def __init__(self, model: GatedGraphNeuralNetwork):
        self.model = model
        
    def explain_feature_importance(self, node_features: torch.Tensor, 
                                 adjacency_matrix: torch.Tensor,
                                 target_node: int) -> Dict[str, float]:
        """
        分析特征重要性
        Analyze feature importance using gradient-based explanation
        """
        node_features.requires_grad_(True)
        
        # Forward pass
        output = self.model(node_features, adjacency_matrix)
        target_output = output[0, target_node, 0]  # Health index for target node
        
        # Backward pass
        target_output.backward()
        
        # Calculate feature importance as gradient magnitude
        gradients = node_features.grad[0, target_node, :].abs()
        
        feature_names = ['temperature', 'ph', 'conductivity', 'chlorophyll']
        importance_dict = {
            name: float(gradients[i]) for i, name in enumerate(feature_names)
        }
        
        return importance_dict

class AdaptiveSecondarysampler:
    """
    自适应二次采样器
    Adaptive secondary sampler
    """
    
    def __init__(self, config: SamplingConfig):
        self.config = config
        
    def identify_secondary_targets(self, all_nodes: Set[int], sampled_nodes: Set[int],
                                 health_indices: Dict[int, float],
                                 health_gradients: Dict[int, float],
                                 recent_interventions: Set[int]) -> Set[int]:
        """
        识别二次采样目标区域
        Identify secondary sampling target regions
        """
        unsampled_nodes = all_nodes - sampled_nodes
        
        # Low health regions
        low_health_nodes = {
            node for node in unsampled_nodes 
            if health_indices.get(node, 1.0) < self.config.health_threshold
        }
        
        # High gradient regions
        high_gradient_nodes = {
            node for node in unsampled_nodes
            if health_gradients.get(node, 0.0) > self.config.gradient_threshold
        }
        
        # Recent intervention regions
        intervention_nodes = unsampled_nodes.intersection(recent_interventions)
        
        # Combine all target regions
        secondary_targets = low_health_nodes.union(high_gradient_nodes).union(intervention_nodes)
        
        logger.info(f"Secondary sampling targets identified:")
        logger.info(f"  Low health regions: {len(low_health_nodes)}")
        logger.info(f"  High gradient regions: {len(high_gradient_nodes)}")
        logger.info(f"  Recent intervention regions: {len(intervention_nodes)}")
        logger.info(f"  Total secondary targets: {len(secondary_targets)}")
        
        return secondary_targets
    
    def calculate_health_gradients(self, positions: np.ndarray, 
                                 health_indices: np.ndarray) -> Dict[int, float]:
        """
        计算健康度梯度
        Calculate health gradients using finite differences
        """
        gradients = {}
        
        for i, pos in enumerate(positions):
            # Find nearby nodes for gradient calculation
            distances = np.linalg.norm(positions - pos, axis=1)
            nearby_indices = np.where((distances > 0) & (distances < 2.0))[0]
            
            if len(nearby_indices) >= 2:
                # Calculate gradient using finite differences
                dx_indices = nearby_indices[np.abs(positions[nearby_indices, 0] - pos[0]) < 1.0]
                dy_indices = nearby_indices[np.abs(positions[nearby_indices, 1] - pos[1]) < 1.0]
                
                grad_x = 0.0
                grad_y = 0.0
                
                if len(dx_indices) >= 2:
                    dx_pos = positions[dx_indices, 0]
                    dx_health = health_indices[dx_indices]
                    if len(dx_pos) > 1:
                        grad_x = np.gradient(dx_health, dx_pos).mean()
                
                if len(dy_indices) >= 2:
                    dy_pos = positions[dy_indices, 1]
                    dy_health = health_indices[dy_indices]
                    if len(dy_pos) > 1:
                        grad_y = np.gradient(dy_health, dy_pos).mean()
                
                gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
                gradients[i] = gradient_magnitude
            else:
                gradients[i] = 0.0
        
        return gradients

class MultiObjectivePathOptimizer:
    """
    多目标路径优化器
    Multi-objective path optimizer
    """

    def __init__(self, config: SamplingConfig):
        self.config = config

    def optimize_path(self, graph: nx.Graph, start_node: int, end_node: int,
                     target_nodes: Set[int], health_indices: Dict[int, float],
                     positions: Dict[int, Tuple[float, float]]) -> Tuple[List[int], Dict[str, float]]:
        """
        多目标路径优化
        Multi-objective path optimization

        Returns:
            optimal_path: List of nodes in optimal path
            metrics: Dictionary of optimization metrics
        """
        # Define objective weights
        w_coverage = 0.4
        w_distance = 0.3
        w_time = 0.2
        w_energy = 0.1

        # Calculate priority weights for target nodes
        priority_weights = self._calculate_priority_weights(target_nodes, health_indices)

        # Use approximation algorithm for large-scale problems
        if len(target_nodes) > 20:
            path = self._greedy_path_optimization(graph, start_node, end_node,
                                                target_nodes, priority_weights, positions)
        else:
            path = self._exact_path_optimization(graph, start_node, end_node,
                                               target_nodes, priority_weights, positions)

        # Calculate metrics
        metrics = self._calculate_path_metrics(path, target_nodes, health_indices, positions)

        return path, metrics

    def _calculate_priority_weights(self, target_nodes: Set[int],
                                  health_indices: Dict[int, float]) -> Dict[int, float]:
        """
        计算采样优先级权重
        Calculate sampling priority weights
        """
        weights = {}
        for node in target_nodes:
            health = health_indices.get(node, 1.0)
            # Higher priority for lower health (inverse relationship)
            weights[node] = 1.0 - health

        return weights

    def _greedy_path_optimization(self, graph: nx.Graph, start_node: int, end_node: int,
                                target_nodes: Set[int], priority_weights: Dict[int, float],
                                positions: Dict[int, Tuple[float, float]]) -> List[int]:
        """
        贪心路径优化算法
        Greedy path optimization algorithm
        """
        path = [start_node]
        remaining_targets = target_nodes.copy()
        current_node = start_node

        while remaining_targets:
            # Find next best target based on combined score
            best_target = None
            best_score = float('inf')

            for target in remaining_targets:
                try:
                    # Calculate shortest path distance
                    shortest_path = nx.shortest_path(graph, current_node, target)
                    distance = len(shortest_path) - 1

                    # Calculate combined score (distance penalty + priority bonus)
                    priority = priority_weights.get(target, 0.0)
                    score = distance - 10 * priority  # Higher priority reduces score

                    if score < best_score:
                        best_score = score
                        best_target = target

                except nx.NetworkXNoPath:
                    continue

            if best_target is None:
                break

            # Add path to best target
            try:
                segment = nx.shortest_path(graph, current_node, best_target)
                path.extend(segment[1:])  # Exclude current_node to avoid duplication
                current_node = best_target
                remaining_targets.remove(best_target)
            except nx.NetworkXNoPath:
                remaining_targets.remove(best_target)

        # Add path to end node
        try:
            final_segment = nx.shortest_path(graph, current_node, end_node)
            path.extend(final_segment[1:])
        except nx.NetworkXNoPath:
            logger.warning(f"No path from {current_node} to end node {end_node}")

        return path

    def _exact_path_optimization(self, graph: nx.Graph, start_node: int, end_node: int,
                               target_nodes: Set[int], priority_weights: Dict[int, float],
                               positions: Dict[int, Tuple[float, float]]) -> List[int]:
        """
        精确路径优化（适用于小规模问题）
        Exact path optimization for small-scale problems
        """
        # For small problems, use TSP-like approach
        nodes_to_visit = [start_node] + list(target_nodes) + [end_node]

        # Calculate distance matrix
        distance_matrix = {}
        for i, node1 in enumerate(nodes_to_visit):
            for j, node2 in enumerate(nodes_to_visit):
                if i != j:
                    try:
                        path_length = nx.shortest_path_length(graph, node1, node2)
                        distance_matrix[(node1, node2)] = path_length
                    except nx.NetworkXNoPath:
                        distance_matrix[(node1, node2)] = float('inf')

        # Simple nearest neighbor heuristic
        path = [start_node]
        remaining = set(target_nodes)
        current = start_node

        while remaining:
            nearest = min(remaining,
                         key=lambda x: distance_matrix.get((current, x), float('inf')))

            # Add shortest path to nearest node
            try:
                segment = nx.shortest_path(graph, current, nearest)
                path.extend(segment[1:])
                current = nearest
                remaining.remove(nearest)
            except nx.NetworkXNoPath:
                remaining.remove(nearest)

        # Add path to end
        try:
            final_segment = nx.shortest_path(graph, current, end_node)
            path.extend(final_segment[1:])
        except nx.NetworkXNoPath:
            pass

        return path

    def _calculate_path_metrics(self, path: List[int], target_nodes: Set[int],
                              health_indices: Dict[int, float],
                              positions: Dict[int, Tuple[float, float]]) -> Dict[str, float]:
        """
        计算路径性能指标
        Calculate path performance metrics
        """
        metrics = {}

        # Coverage efficiency
        visited_targets = set(path).intersection(target_nodes)
        metrics['coverage_efficiency'] = len(visited_targets) / len(target_nodes) if target_nodes else 1.0

        # Path length
        total_distance = 0.0
        for i in range(len(path) - 1):
            pos1 = positions.get(path[i], (0, 0))
            pos2 = positions.get(path[i+1], (0, 0))
            distance = np.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)
            total_distance += distance

        metrics['total_distance'] = total_distance
        metrics['path_length'] = len(path)

        # Health coverage (weighted by health indices)
        total_health_weight = sum(1.0 - health_indices.get(node, 1.0) for node in visited_targets)
        max_possible_weight = sum(1.0 - health_indices.get(node, 1.0) for node in target_nodes)
        metrics['health_coverage'] = total_health_weight / max_possible_weight if max_possible_weight > 0 else 1.0

        # Estimated time and energy (simplified)
        metrics['estimated_time'] = total_distance / 2.0 + len(visited_targets) * 5.0  # 2 m/s speed + 5s per sample
        metrics['estimated_energy'] = total_distance * 0.1 + len(visited_targets) * 2.0  # Energy units

        return metrics

class RealTimeMonitoringSystem:
    """
    实时监测与预警系统
    Real-time monitoring and alert system
    """

    def __init__(self, config: SamplingConfig):
        self.config = config
        self.alert_history = []

    def evaluate_alerts(self, health_indices: Dict[int, float]) -> Dict[int, str]:
        """
        评估预警级别
        Evaluate alert levels
        """
        alerts = {}

        for node, health in health_indices.items():
            if health < 0.3:
                alerts[node] = 'Critical'
            elif health < 0.6:
                alerts[node] = 'Warning'
            else:
                alerts[node] = 'Normal'

        # Log critical alerts
        critical_nodes = [node for node, alert in alerts.items() if alert == 'Critical']
        if critical_nodes:
            logger.critical(f"Critical health alert for nodes: {critical_nodes}")
            self.alert_history.append({
                'timestamp': np.datetime64('now'),
                'type': 'Critical',
                'nodes': critical_nodes
            })

        return alerts

    def calculate_system_metrics(self, sampled_nodes: Set[int], total_nodes: int,
                               true_positives: int, false_negatives: int,
                               path_length: float, direct_distance: float,
                               useful_energy: float, total_energy: float) -> Dict[str, float]:
        """
        计算系统性能指标
        Calculate system performance metrics
        """
        metrics = {}

        # Coverage efficiency
        metrics['coverage_efficiency'] = len(sampled_nodes) / total_nodes

        # Detection accuracy
        if true_positives + false_negatives > 0:
            metrics['detection_accuracy'] = true_positives / (true_positives + false_negatives)
        else:
            metrics['detection_accuracy'] = 1.0

        # Path efficiency
        if path_length > 0:
            metrics['path_efficiency'] = direct_distance / path_length
        else:
            metrics['path_efficiency'] = 1.0

        # Energy efficiency
        if total_energy > 0:
            metrics['energy_efficiency'] = useful_energy / total_energy
        else:
            metrics['energy_efficiency'] = 1.0

        return metrics
