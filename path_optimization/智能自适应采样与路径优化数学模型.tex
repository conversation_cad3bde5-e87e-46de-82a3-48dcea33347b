\documentclass[12pt]{article}
\usepackage[utf8]{inputenc}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{geometry}
\usepackage{xeCJK}
\usepackage{enumitem}

\geometry{a4paper, margin=2.5cm}

\title{智能自适应采样与路径优化的数学模型}
\author{Pearl Farming Monitoring System}
\date{\today}

\begin{document}

\maketitle

\section*{智能自适应采样与路径优化的数学模型}

\subsection*{1. 问题定义与符号说明}
本模型将传统的靶向复核路径规划扩展为一个集成了实时采样、智能分析和自适应决策的多阶段优化问题。系统通过稀疏采样、数据插值、门控图神经网络分析和自适应二次采样，实现对珍珠养殖环境的智能监测与优化路径规划。

\subsubsection*{1.1 基础集合与参数}
\begin{itemize}
    \item \textbf{$N$}: 养殖水域中所有离散化节点的集合，$|N| = n$。
    \item \textbf{$A$}: \textbf{机器人所有可通行路径构成的有向弧（arc）集合。} 基于梯形网络结构，$A$ 中包含沿着预设梯形航道移动的弧，支持列向扫描和边缘连接。
    \item \textbf{$D = \{d_1, d_2, \ldots, d_m\}$}: 采样深度层级集合，$m$ 为最大采样深度层数。
    \item \textbf{$N_D = N \times D$}: 扩展节点集合，考虑多深度采样，$|N_D| = n \times m$。
    \item \textbf{$P^{(0)} \subseteq N$}: 初始稀疏采样目标点集合。
    \item \textbf{$P^{(k)} \subseteq N$}: 第 $k$ 轮自适应采样目标点集合。
    \item \textbf{$s$}: 路径的起始节点（机器人当前位置），$s \in N$。
    \item \textbf{$e$}: 路径的终点节点（例如母港），$e \in N$。
    \item \textbf{$H_i^{(k)}$}: 节点 $i$ 在第 $k$ 轮的健康度指数，$H_i^{(k)} \in [0, 1]$。
    \item \textbf{$\theta_h$}: 健康度阈值，触发深度分析，通常设为 $0.6$。
    \item \textbf{$d_{ij}$}: 沿弧 $(i, j)$ 移动的物理距离。
    \item \textbf{$t_{ij}$}: 沿弧 $(i, j)$ 移动所需的时间。
    \item \textbf{$E_i$}: 节点 $i$ 的能耗系数。
\end{itemize}

\subsubsection*{1.2 环境参数与传感器数据}
\begin{itemize}
    \item \textbf{$\mathbf{X}_i = [T_i, pH_i, EC_i, Chl_i]^T$}: 节点 $i$ 的四维环境特征向量（温度、pH、电导率、叶绿素）。
    \item \textbf{$\mathbf{X}_i^{(d)}$}: 节点 $i$ 在深度 $d$ 的环境特征向量。
    \item \textbf{$\mathcal{S}^{(k)}$}: 第 $k$ 轮已采样节点集合。
    \item \textbf{$\mathcal{U}^{(k)} = N \setminus \mathcal{S}^{(k)}$}: 第 $k$ 轮未采样节点集合。
    \item \textbf{$BC_i$}: 节点 $i$ 的介数中心性（betweenness centrality）。
    \item \textbf{$\nabla H_i$}: 节点 $i$ 处的健康度梯度。
\end{itemize}

\subsubsection*{1.3 决策变量}
\begin{itemize}
    \item \textbf{$x_{ij}^{(k)}$}: 第 $k$ 轮采样中，机器人从节点 $i$ 到节点 $j$ 的路径选择变量。
    \item \textbf{$y_i^{(k)}$}: 第 $k$ 轮中节点 $i$ 是否被采样的二元变量。
    \item \textbf{$z_i^{(d,k)}$}: 第 $k$ 轮中节点 $i$ 在深度 $d$ 是否被采样的二元变量。
    \item \textbf{$u_i^{(k)}$}: MTZ 子回路消除辅助变量。
    \item \textbf{$\alpha_i^{(k)}$}: 节点 $i$ 的采样优先级权重。
\end{itemize}

\subsection*{2. 稀疏采样策略设计}

\subsubsection*{2.1 介数中心性采样选择}
基于图论的介数中心性算法识别关键节点：
\begin{equation}
BC_i = \sum_{s \neq i \neq t \in N} \frac{\sigma_{st}(i)}{\sigma_{st}}
\end{equation}
其中 $\sigma_{st}$ 是节点 $s$ 到 $t$ 的最短路径数，$\sigma_{st}(i)$ 是经过节点 $i$ 的最短路径数。

初始采样点选择策略：
\begin{equation}
P^{(0)} = \{i \in N : BC_i \geq \text{percentile}(BC, 80\%)\} \cup \{珍珠位置节点\}
\end{equation}

\subsubsection*{2.2 多深度采样决策}
对于每个选中的采样点 $i \in P^{(0)}$，深度采样决策：
\begin{equation}
z_i^{(d,0)} = \begin{cases}
1, & \text{if } d \in \{d_{\text{surface}}, d_{\text{middle}}, d_{\text{bottom}}\} \\
1, & \text{if } BC_i \geq \theta_{BC} \text{ and } d \in D \\
0, & \text{otherwise}
\end{cases}
\end{equation}

\subsection*{3. 数据插值与健康度计算}

\subsubsection*{3.1 反距离权重插值（IDW）}
对未采样节点 $j \in \mathcal{U}^{(k)}$，使用 IDW 方法估算环境参数：
\begin{equation}
\hat{\mathbf{X}}_j = \frac{\sum_{i \in \mathcal{S}^{(k)}} w_{ij} \mathbf{X}_i}{\sum_{i \in \mathcal{S}^{(k)}} w_{ij}}
\end{equation}
其中权重函数：
\begin{equation}
w_{ij} = \frac{1}{d_{ij}^p + \epsilon}
\end{equation}
$p$ 为距离衰减指数（通常取 2），$\epsilon$ 为防止除零的小常数。

\subsubsection*{3.2 生物数学健康度模型}
基于环境参数计算健康度指数：
\begin{equation}
H_i^{(k)} = \prod_{f=1}^{4} \left( 1 - \exp\left(-\frac{(\mathbf{X}_{i,f} - \mu_f)^2}{2\sigma_f^2}\right) \right)^{w_f}
\end{equation}
其中 $\mathbf{X}_{i,f}$ 是第 $f$ 个环境因子，$\mu_f, \sigma_f$ 是最适参数，$w_f$ 是权重系数。

\subsection*{4. 门控图神经网络（GGNN）分析}

\subsubsection*{4.1 GGNN 算法流程}
当检测到 $H_i^{(k)} < \theta_h$ 时，启动 GGNN 进行因果分析：

\textbf{步骤 1：图构建}
构建空间邻接图 $G = (V, E)$，其中：
\begin{equation}
E = \{(i,j) : d_{ij} \leq r_{\text{neighbor}} \text{ or } (i,j) \in A\}
\end{equation}

\textbf{步骤 2：节点特征初始化}
\begin{equation}
\mathbf{h}_i^{(0)} = \mathbf{W}_{\text{init}} \mathbf{X}_i + \mathbf{b}_{\text{init}}
\end{equation}

\textbf{步骤 3：门控更新机制}
对于每个时间步 $t = 1, \ldots, T$：
\begin{align}
\mathbf{a}_i^{(t)} &= \sum_{j \in \mathcal{N}(i)} \mathbf{W}_{\text{msg}} \mathbf{h}_j^{(t-1)} \\
\mathbf{z}_i^{(t)} &= \sigma(\mathbf{W}_z [\mathbf{h}_i^{(t-1)}, \mathbf{a}_i^{(t)}] + \mathbf{b}_z) \\
\mathbf{r}_i^{(t)} &= \sigma(\mathbf{W}_r [\mathbf{h}_i^{(t-1)}, \mathbf{a}_i^{(t)}] + \mathbf{b}_r) \\
\tilde{\mathbf{h}}_i^{(t)} &= \tanh(\mathbf{W}_h [\mathbf{r}_i^{(t)} \odot \mathbf{h}_i^{(t-1)}, \mathbf{a}_i^{(t)}] + \mathbf{b}_h) \\
\mathbf{h}_i^{(t)} &= (1 - \mathbf{z}_i^{(t)}) \odot \mathbf{h}_i^{(t-1)} + \mathbf{z}_i^{(t)} \odot \tilde{\mathbf{h}}_i^{(t)}
\end{align}

\subsubsection*{4.2 注意力机制（可选）}
引入注意力机制增强特征重要性：
\begin{align}
\mathbf{e}_{ij}^{(t)} &= \text{LeakyReLU}(\mathbf{W}_{\text{att}} [\mathbf{h}_i^{(t)} \| \mathbf{h}_j^{(t)}]) \\
\alpha_{ij}^{(t)} &= \frac{\exp(\mathbf{e}_{ij}^{(t)})}{\sum_{k \in \mathcal{N}(i)} \exp(\mathbf{e}_{ik}^{(t)})} \\
\mathbf{a}_i^{(t)} &= \sum_{j \in \mathcal{N}(i)} \alpha_{ij}^{(t)} \mathbf{W}_{\text{msg}} \mathbf{h}_j^{(t-1)}
\end{align}

\subsubsection*{4.3 特征重要性分析（GNNExplainer）}
使用 GNNExplainer 识别关键环境因子：
\begin{equation}
\text{Importance}(f) = \frac{1}{|\mathcal{S}^{(k)}|} \sum_{i \in \mathcal{S}^{(k)}} \left| \frac{\partial H_i^{(k)}}{\partial \mathbf{X}_{i,f}} \right|
\end{equation}

\subsection*{5. 自适应二次采样策略}

\subsubsection*{5.1 二次采样目标识别}
识别需要二次采样的区域：
\begin{align}
P_{\text{low}}^{(k+1)} &= \{i \in \mathcal{U}^{(k)} : \hat{H}_i^{(k)} < \theta_h\} \\
P_{\text{grad}}^{(k+1)} &= \{i \in \mathcal{U}^{(k)} : \|\nabla H_i\| > \theta_{\text{grad}}\} \\
P_{\text{action}}^{(k+1)} &= \{i \in \mathcal{U}^{(k)} : \text{近期有人工干预}\} \\
P^{(k+1)} &= P_{\text{low}}^{(k+1)} \cup P_{\text{grad}}^{(k+1)} \cup P_{\text{action}}^{(k+1)}
\end{align}

\subsubsection*{5.2 健康度梯度计算}
使用有限差分方法计算健康度梯度：
\begin{equation}
\nabla H_i = \left[ \frac{\partial H_i}{\partial x}, \frac{\partial H_i}{\partial y} \right] \approx \left[ \frac{H_{i+\Delta x} - H_{i-\Delta x}}{2\Delta x}, \frac{H_{i+\Delta y} - H_{i-\Delta y}}{2\Delta y} \right]
\end{equation}

\subsection*{6. 多目标优化路径规划}

\subsubsection*{6.1 综合目标函数}
设计多目标优化函数，同时优化采样覆盖率、路径长度、采样时间和能耗：
\begin{equation}
\min J^{(k)} = w_1 J_{\text{coverage}}^{(k)} + w_2 J_{\text{distance}}^{(k)} + w_3 J_{\text{time}}^{(k)} + w_4 J_{\text{energy}}^{(k)}
\end{equation}

其中各子目标函数定义为：

\textbf{采样覆盖率目标：}
\begin{equation}
J_{\text{coverage}}^{(k)} = -\sum_{i \in P^{(k)}} \alpha_i^{(k)} y_i^{(k)} + \lambda_1 \sum_{i \in \mathcal{U}^{(k)}} (1 - H_i^{(k)})^2
\end{equation}

\textbf{路径长度目标：}
\begin{equation}
J_{\text{distance}}^{(k)} = \sum_{(i,j) \in A} d_{ij} x_{ij}^{(k)}
\end{equation}

\textbf{采样时间目标：}
\begin{equation}
J_{\text{time}}^{(k)} = \sum_{(i,j) \in A} t_{ij} x_{ij}^{(k)} + \sum_{i \in P^{(k)}} \sum_{d \in D} t_{\text{sample}} z_i^{(d,k)}
\end{equation}

\textbf{能耗目标：}
\begin{equation}
J_{\text{energy}}^{(k)} = \sum_{(i,j) \in A} E_{ij} x_{ij}^{(k)} + \sum_{i \in P^{(k)}} \sum_{d \in D} E_{\text{sample}} z_i^{(d,k)}
\end{equation}

\subsubsection*{6.2 约束条件}
\begin{align}
% 流量守恒约束
\sum_{j: (i,j) \in A} x_{ij}^{(k)} - \sum_{j: (j,i) \in A} x_{ji}^{(k)} &= 0, \quad \forall i \in N \setminus \{s, e\} \\
% 起点和终点约束
\sum_{j: (s,j) \in A} x_{sj}^{(k)} &= 1 \\
\sum_{j: (j,e) \in A} x_{je}^{(k)} &= 1 \\
% 采样点访问约束
\sum_{j: (i,j) \in A} x_{ij}^{(k)} &\geq y_i^{(k)}, \quad \forall i \in P^{(k)} \\
% 深度采样约束
\sum_{d \in D} z_i^{(d,k)} &\geq y_i^{(k)}, \quad \forall i \in P^{(k)} \\
% 采样能力约束
\sum_{i \in P^{(k)}} \sum_{d \in D} z_i^{(d,k)} &\leq C_{\text{sample}} \\
% 时间窗约束
\sum_{(i,j) \in A} t_{ij} x_{ij}^{(k)} + \sum_{i \in P^{(k)}} \sum_{d \in D} t_{\text{sample}} z_i^{(d,k)} &\leq T_{\text{max}} \\
% 子回路消除约束（MTZ）
u_i^{(k)} - u_j^{(k)} + |N| x_{ij}^{(k)} &\leq |N| - 1, \quad \forall (i,j) \in A, i \neq s, j \neq s \\
% 变量域约束
x_{ij}^{(k)} &\in \{0, 1\}, \quad \forall (i,j) \in A \\
y_i^{(k)}, z_i^{(d,k)} &\in \{0, 1\}, \quad \forall i \in N, d \in D \\
u_i^{(k)} &\geq 0, \quad \forall i \in N
\end{align}

\subsection*{7. 系统集成与实时决策}

\subsubsection*{7.1 多阶段决策流程}
系统采用滚动时域优化策略：
\begin{enumerate}
\item \textbf{初始化阶段}：基于介数中心性选择初始采样点 $P^{(0)}$
\item \textbf{采样执行阶段}：按优化路径执行采样，获取实时数据
\item \textbf{数据分析阶段}：使用 IDW 插值和 GGNN 分析健康度
\item \textbf{自适应决策阶段}：识别二次采样需求，更新目标点集合
\item \textbf{路径重规划阶段}：基于新信息重新优化路径
\end{enumerate}

\subsubsection*{7.2 实时预警机制}
定义预警触发条件：
\begin{equation}
\text{Alert}(i) = \begin{cases}
\text{Critical}, & \text{if } H_i^{(k)} < 0.3 \\
\text{Warning}, & \text{if } 0.3 \leq H_i^{(k)} < 0.6 \\
\text{Normal}, & \text{if } H_i^{(k)} \geq 0.6
\end{cases}
\end{equation}

\subsubsection*{7.3 系统性能指标}
\begin{align}
\text{Coverage Efficiency} &= \frac{|\mathcal{S}^{(k)}|}{|N|} \\
\text{Detection Accuracy} &= \frac{\text{True Positives}}{\text{True Positives} + \text{False Negatives}} \\
\text{Path Efficiency} &= \frac{\text{Direct Distance}}{\text{Actual Path Length}} \\
\text{Energy Efficiency} &= \frac{\text{Useful Sampling Energy}}{\text{Total Energy Consumption}}
\end{align}

\end{document}
