#!/usr/bin/env python3
"""
集成智能采样系统
Integrated Intelligent Sampling System

This module integrates all components of the adaptive sampling system:
- Sparse sampling based on betweenness centrality
- Multi-depth sampling strategy
- IDW interpolation for unsampled nodes
- GGNN analysis for causal relationships
- Adaptive secondary sampling
- Multi-objective path optimization
- Real-time monitoring and alerts

Author: Pearl Farming Monitoring System
Date: 2025-01-26
"""

import numpy as np
import networkx as nx
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Set, Optional
import logging
from dataclasses import dataclass
import time

from adaptive_sampling_system import (
    SamplingConfig, BetweennessCentralitySampler, MultiDepthSampler,
    IDWInterpolator, HealthIndexCalculator, GatedGraphNeuralNetwork,
    GNNExplainer, AdaptiveSecondarysampler, MultiObjectivePathOptimizer,
    RealTimeMonitoringSystem
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class SamplingRound:
    """Data structure for storing sampling round information"""
    round_id: int
    sampled_nodes: Set[int]
    environmental_data: Dict[int, np.ndarray]
    health_indices: Dict[int, float]
    path: List[int]
    metrics: Dict[str, float]
    alerts: Dict[int, str]
    timestamp: float

class IntegratedSamplingSystem:
    """
    集成智能采样系统主类
    Main class for integrated intelligent sampling system
    """
    
    def __init__(self, pond_graph: nx.Graph, pearl_positions: List[int], 
                 node_positions: Dict[int, Tuple[float, float]], config: SamplingConfig = None):
        """
        Initialize the integrated sampling system
        
        Args:
            pond_graph: NetworkX graph representing the pond structure
            pearl_positions: List of node IDs where pearls are located
            node_positions: Dictionary mapping node IDs to (x, y) coordinates
            config: Sampling configuration parameters
        """
        self.pond_graph = pond_graph
        self.pearl_positions = pearl_positions
        self.node_positions = node_positions
        self.config = config or SamplingConfig()
        
        # Initialize components
        self.centrality_sampler = BetweennessCentralitySampler(pond_graph, pearl_positions)
        self.depth_sampler = MultiDepthSampler(self.config)
        self.interpolator = IDWInterpolator(power=self.config.idw_power)
        self.health_calculator = HealthIndexCalculator()
        self.secondary_sampler = AdaptiveSecondarysampler(self.config)
        self.path_optimizer = MultiObjectivePathOptimizer(self.config)
        self.monitoring_system = RealTimeMonitoringSystem(self.config)
        
        # Initialize GGNN
        self.ggnn = GatedGraphNeuralNetwork(
            input_dim=4,  # temperature, pH, conductivity, chlorophyll
            hidden_dim=64,
            output_dim=1,  # health index
            num_layers=3,
            use_attention=True
        )
        self.gnn_explainer = GNNExplainer(self.ggnn)
        
        # System state
        self.sampling_history: List[SamplingRound] = []
        self.current_round = 0
        self.all_sampled_nodes: Set[int] = set()
        
        logger.info("Integrated Sampling System initialized")
        logger.info(f"Pond graph: {len(pond_graph.nodes)} nodes, {len(pond_graph.edges)} edges")
        logger.info(f"Pearl positions: {len(pearl_positions)}")
        logger.info(f"Configuration: {self.config}")
    
    def execute_sampling_campaign(self, start_node: int, end_node: int, 
                                max_rounds: int = 3) -> List[SamplingRound]:
        """
        执行完整的采样活动
        Execute complete sampling campaign
        
        Args:
            start_node: Starting position for robot
            end_node: Ending position for robot
            max_rounds: Maximum number of sampling rounds
            
        Returns:
            List of sampling rounds with results
        """
        logger.info(f"Starting sampling campaign: {max_rounds} rounds")
        logger.info(f"Start node: {start_node}, End node: {end_node}")
        
        for round_id in range(max_rounds):
            logger.info(f"\n{'='*50}")
            logger.info(f"SAMPLING ROUND {round_id + 1}")
            logger.info(f"{'='*50}")
            
            sampling_round = self._execute_single_round(round_id, start_node, end_node)
            self.sampling_history.append(sampling_round)
            
            # Check if early termination is needed
            if self._should_terminate_early(sampling_round):
                logger.info("Early termination criteria met")
                break
        
        logger.info(f"\nSampling campaign completed: {len(self.sampling_history)} rounds")
        return self.sampling_history
    
    def _execute_single_round(self, round_id: int, start_node: int, end_node: int) -> SamplingRound:
        """
        执行单轮采样
        Execute single sampling round
        """
        start_time = time.time()
        
        # Step 1: Determine sampling targets
        if round_id == 0:
            # Initial sparse sampling based on centrality
            target_nodes = self._initial_sampling_selection()
        else:
            # Adaptive secondary sampling
            target_nodes = self._adaptive_sampling_selection()
        
        logger.info(f"Round {round_id + 1}: Selected {len(target_nodes)} target nodes")
        
        # Step 2: Optimize sampling path
        optimal_path, path_metrics = self.path_optimizer.optimize_path(
            self.pond_graph, start_node, end_node, target_nodes,
            self._get_current_health_indices(), self.node_positions
        )
        
        logger.info(f"Optimized path length: {len(optimal_path)} nodes")
        logger.info(f"Path metrics: {path_metrics}")
        
        # Step 3: Simulate sampling execution
        sampled_data = self._simulate_sampling_execution(optimal_path, target_nodes)
        
        # Step 4: Update system state
        self.all_sampled_nodes.update(sampled_data.keys())
        
        # Step 5: Interpolate data for unsampled nodes
        interpolated_data = self._interpolate_unsampled_data(sampled_data)
        
        # Step 6: Calculate health indices
        all_environmental_data = {**sampled_data, **interpolated_data}
        health_indices = self._calculate_health_indices(all_environmental_data)
        
        # Step 7: GGNN analysis for low-health regions
        ggnn_results = self._perform_ggnn_analysis(all_environmental_data, health_indices)
        
        # Step 8: Generate alerts
        alerts = self.monitoring_system.evaluate_alerts(health_indices)
        
        # Step 9: Calculate system metrics
        system_metrics = self._calculate_system_metrics(sampled_data, path_metrics)
        
        # Create sampling round record
        sampling_round = SamplingRound(
            round_id=round_id,
            sampled_nodes=set(sampled_data.keys()),
            environmental_data=sampled_data,
            health_indices=health_indices,
            path=optimal_path,
            metrics={**path_metrics, **system_metrics, **ggnn_results},
            alerts=alerts,
            timestamp=time.time()
        )
        
        execution_time = time.time() - start_time
        logger.info(f"Round {round_id + 1} completed in {execution_time:.2f} seconds")
        
        return sampling_round
    
    def _initial_sampling_selection(self) -> Set[int]:
        """
        初始稀疏采样选择
        Initial sparse sampling selection
        """
        logger.info("Performing initial sampling selection based on betweenness centrality")
        
        # Select based on centrality and pearl positions
        initial_targets = self.centrality_sampler.select_initial_sampling_points(percentile=80.0)
        
        # Limit to sampling capacity
        if len(initial_targets) > self.config.max_sampling_capacity:
            # Prioritize pearl positions and highest centrality nodes
            centrality_scores = self.centrality_sampler.centrality_scores
            sorted_targets = sorted(initial_targets, 
                                  key=lambda x: (x in self.pearl_positions, centrality_scores.get(x, 0)),
                                  reverse=True)
            initial_targets = set(sorted_targets[:self.config.max_sampling_capacity])
        
        return initial_targets
    
    def _adaptive_sampling_selection(self) -> Set[int]:
        """
        自适应二次采样选择
        Adaptive secondary sampling selection
        """
        logger.info("Performing adaptive secondary sampling selection")
        
        # Get current health indices and gradients
        current_health = self._get_current_health_indices()
        positions_array = np.array([self.node_positions[i] for i in sorted(self.node_positions.keys())])
        health_array = np.array([current_health.get(i, 1.0) for i in sorted(self.node_positions.keys())])
        
        health_gradients = self.secondary_sampler.calculate_health_gradients(positions_array, health_array)
        
        # Identify secondary targets
        all_nodes = set(self.pond_graph.nodes())
        recent_interventions = set()  # This would come from external system
        
        secondary_targets = self.secondary_sampler.identify_secondary_targets(
            all_nodes, self.all_sampled_nodes, current_health, health_gradients, recent_interventions
        )
        
        # Limit to remaining capacity
        remaining_capacity = self.config.max_sampling_capacity - len(self.all_sampled_nodes)
        if len(secondary_targets) > remaining_capacity:
            # Prioritize by health score (lower is higher priority)
            sorted_targets = sorted(secondary_targets, 
                                  key=lambda x: current_health.get(x, 1.0))
            secondary_targets = set(sorted_targets[:remaining_capacity])
        
        return secondary_targets
    
    def _simulate_sampling_execution(self, path: List[int], target_nodes: Set[int]) -> Dict[int, np.ndarray]:
        """
        模拟采样执行过程
        Simulate sampling execution process
        """
        logger.info("Simulating sampling execution")
        
        sampled_data = {}
        
        for node in path:
            if node in target_nodes:
                # Simulate multi-depth sampling
                centrality_score = self.centrality_sampler.centrality_scores.get(node, 0.0)
                is_pearl = node in self.pearl_positions
                
                depths = self.depth_sampler.decide_sampling_depths(node, centrality_score, is_pearl)
                
                # Generate synthetic environmental data for each depth
                depth_data = []
                for depth in depths:
                    # Simulate realistic environmental parameters with depth variation
                    base_temp = 25.0 + np.random.normal(0, 2.0)
                    temp = base_temp - depth * 0.5  # Temperature decreases with depth
                    
                    ph = 7.5 + np.random.normal(0, 0.3)
                    conductivity = 500.0 + np.random.normal(0, 50.0)
                    chlorophyll = max(0, 10.0 + np.random.normal(0, 3.0) - depth * 0.5)
                    
                    depth_data.append([temp, ph, conductivity, chlorophyll])
                
                # Average across depths for node-level data
                sampled_data[node] = np.mean(depth_data, axis=0)
        
        logger.info(f"Sampled {len(sampled_data)} nodes with environmental data")
        return sampled_data
    
    def _interpolate_unsampled_data(self, sampled_data: Dict[int, np.ndarray]) -> Dict[int, np.ndarray]:
        """
        插值未采样节点数据
        Interpolate data for unsampled nodes
        """
        if not sampled_data:
            return {}
        
        logger.info("Interpolating data for unsampled nodes using IDW")
        
        # Prepare data for interpolation
        sampled_nodes = list(sampled_data.keys())
        sampled_positions = np.array([self.node_positions[node] for node in sampled_nodes])
        sampled_values = np.array([sampled_data[node] for node in sampled_nodes])
        
        # Get unsampled nodes
        all_nodes = set(self.pond_graph.nodes())
        unsampled_nodes = all_nodes - set(sampled_data.keys())
        
        if not unsampled_nodes:
            return {}
        
        unsampled_positions = np.array([self.node_positions[node] for node in unsampled_nodes])
        
        # Perform interpolation
        interpolated_values = self.interpolator.interpolate(
            sampled_positions, sampled_values, unsampled_positions
        )
        
        # Create result dictionary
        interpolated_data = {
            node: interpolated_values[i] 
            for i, node in enumerate(unsampled_nodes)
        }
        
        logger.info(f"Interpolated data for {len(interpolated_data)} unsampled nodes")
        return interpolated_data

    def _calculate_health_indices(self, environmental_data: Dict[int, np.ndarray]) -> Dict[int, float]:
        """
        计算健康度指数
        Calculate health indices for all nodes
        """
        logger.info("Calculating health indices using biological mathematical model")

        if not environmental_data:
            return {}

        # Prepare data array
        nodes = list(environmental_data.keys())
        data_array = np.array([environmental_data[node] for node in nodes])

        # Calculate health indices
        health_array = self.health_calculator.calculate_health_index(data_array)

        # Create result dictionary
        health_indices = {node: float(health_array[i]) for i, node in enumerate(nodes)}

        # Log statistics
        avg_health = np.mean(health_array)
        min_health = np.min(health_array)
        max_health = np.max(health_array)

        logger.info(f"Health indices calculated: avg={avg_health:.3f}, min={min_health:.3f}, max={max_health:.3f}")

        return health_indices

    def _perform_ggnn_analysis(self, environmental_data: Dict[int, np.ndarray],
                             health_indices: Dict[int, float]) -> Dict[str, float]:
        """
        执行GGNN分析
        Perform GGNN analysis for causal relationships
        """
        logger.info("Performing GGNN analysis for causal relationships")

        # Filter nodes with low health for analysis
        low_health_nodes = [node for node, health in health_indices.items()
                           if health < self.config.health_threshold]

        if not low_health_nodes:
            logger.info("No low-health nodes detected, skipping GGNN analysis")
            return {'ggnn_analyzed_nodes': 0, 'feature_importance': {}}

        logger.info(f"Analyzing {len(low_health_nodes)} low-health nodes with GGNN")

        # Prepare graph data for GGNN
        nodes = list(environmental_data.keys())
        node_to_idx = {node: i for i, node in enumerate(nodes)}

        # Create adjacency matrix
        n_nodes = len(nodes)
        adjacency_matrix = np.zeros((n_nodes, n_nodes))

        for node1 in nodes:
            for node2 in nodes:
                if node1 != node2:
                    # Check if nodes are connected in graph or within neighbor radius
                    if (self.pond_graph.has_edge(node1, node2) or
                        self._calculate_distance(node1, node2) <= self.config.neighbor_radius):
                        adjacency_matrix[node_to_idx[node1], node_to_idx[node2]] = 1.0

        # Prepare input tensors
        node_features = torch.tensor([environmental_data[node] for node in nodes], dtype=torch.float32)
        adjacency_tensor = torch.tensor(adjacency_matrix, dtype=torch.float32)

        # Add batch dimension
        node_features = node_features.unsqueeze(0)  # (1, n_nodes, 4)
        adjacency_tensor = adjacency_tensor.unsqueeze(0)  # (1, n_nodes, n_nodes)

        # GGNN forward pass
        with torch.no_grad():
            ggnn_output = self.ggnn(node_features, adjacency_tensor)
            predicted_health = torch.sigmoid(ggnn_output).squeeze().numpy()

        # Feature importance analysis for low-health nodes
        feature_importance_results = {}

        for node in low_health_nodes[:5]:  # Analyze top 5 low-health nodes
            if node in node_to_idx:
                node_idx = node_to_idx[node]

                # Create input for explanation
                explanation_features = node_features.clone()
                explanation_features.requires_grad_(True)

                importance = self.gnn_explainer.explain_feature_importance(
                    explanation_features, adjacency_tensor, node_idx
                )

                feature_importance_results[f'node_{node}'] = importance

        # Aggregate feature importance across analyzed nodes
        if feature_importance_results:
            feature_names = ['temperature', 'ph', 'conductivity', 'chlorophyll']
            aggregated_importance = {}

            for feature in feature_names:
                importance_values = [result[feature] for result in feature_importance_results.values()]
                aggregated_importance[feature] = float(np.mean(importance_values))
        else:
            aggregated_importance = {}

        logger.info(f"GGNN analysis completed. Feature importance: {aggregated_importance}")

        return {
            'ggnn_analyzed_nodes': len(low_health_nodes),
            'feature_importance': aggregated_importance,
            'ggnn_prediction_accuracy': self._calculate_ggnn_accuracy(predicted_health, health_indices, nodes)
        }

    def _calculate_distance(self, node1: int, node2: int) -> float:
        """
        计算两个节点之间的欧几里得距离
        Calculate Euclidean distance between two nodes
        """
        pos1 = self.node_positions.get(node1, (0, 0))
        pos2 = self.node_positions.get(node2, (0, 0))
        return np.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)

    def _calculate_ggnn_accuracy(self, predicted_health: np.ndarray,
                               actual_health: Dict[int, float], nodes: List[int]) -> float:
        """
        计算GGNN预测准确性
        Calculate GGNN prediction accuracy
        """
        if len(predicted_health) != len(nodes):
            return 0.0

        actual_values = np.array([actual_health.get(node, 0.0) for node in nodes])
        mse = np.mean((predicted_health - actual_values)**2)

        return float(1.0 / (1.0 + mse))  # Convert MSE to accuracy-like metric

    def _calculate_system_metrics(self, sampled_data: Dict[int, np.ndarray],
                                path_metrics: Dict[str, float]) -> Dict[str, float]:
        """
        计算系统性能指标
        Calculate system performance metrics
        """
        total_nodes = len(self.pond_graph.nodes())
        sampled_nodes_count = len(self.all_sampled_nodes)

        # Simulate detection metrics (in real system, these would come from validation)
        true_positives = max(1, int(sampled_nodes_count * 0.8))  # Simulated
        false_negatives = max(0, int(sampled_nodes_count * 0.1))  # Simulated

        # Calculate direct distance (straight line from start to end)
        path = path_metrics.get('total_distance', 100.0)
        direct_distance = path * 0.6  # Simulated direct distance

        # Energy metrics (simulated)
        useful_energy = len(sampled_data) * 2.0  # Energy for useful sampling
        total_energy = useful_energy + path_metrics.get('estimated_energy', 50.0)

        system_metrics = self.monitoring_system.calculate_system_metrics(
            self.all_sampled_nodes, total_nodes, true_positives, false_negatives,
            path, direct_distance, useful_energy, total_energy
        )

        return system_metrics

    def _get_current_health_indices(self) -> Dict[int, float]:
        """
        获取当前健康度指数
        Get current health indices from latest sampling round
        """
        if not self.sampling_history:
            # Return default health indices for all nodes
            return {node: 1.0 for node in self.pond_graph.nodes()}

        return self.sampling_history[-1].health_indices

    def _should_terminate_early(self, sampling_round: SamplingRound) -> bool:
        """
        判断是否应该提前终止采样
        Determine if sampling should terminate early
        """
        # Terminate if coverage is sufficient and no critical alerts
        coverage_threshold = 0.8
        critical_alerts = sum(1 for alert in sampling_round.alerts.values() if alert == 'Critical')

        coverage_efficiency = sampling_round.metrics.get('coverage_efficiency', 0.0)

        return coverage_efficiency >= coverage_threshold and critical_alerts == 0

    def generate_comprehensive_report(self) -> Dict:
        """
        生成综合报告
        Generate comprehensive system report
        """
        if not self.sampling_history:
            return {"error": "No sampling data available"}

        report = {
            "campaign_summary": {
                "total_rounds": len(self.sampling_history),
                "total_sampled_nodes": len(self.all_sampled_nodes),
                "total_nodes": len(self.pond_graph.nodes()),
                "final_coverage": len(self.all_sampled_nodes) / len(self.pond_graph.nodes())
            },
            "round_details": [],
            "system_performance": {},
            "health_analysis": {},
            "recommendations": []
        }

        # Compile round details
        for round_data in self.sampling_history:
            round_summary = {
                "round_id": round_data.round_id,
                "sampled_nodes_count": len(round_data.sampled_nodes),
                "path_length": len(round_data.path),
                "alerts": {
                    "critical": sum(1 for alert in round_data.alerts.values() if alert == 'Critical'),
                    "warning": sum(1 for alert in round_data.alerts.values() if alert == 'Warning'),
                    "normal": sum(1 for alert in round_data.alerts.values() if alert == 'Normal')
                },
                "metrics": round_data.metrics
            }
            report["round_details"].append(round_summary)

        # System performance analysis
        latest_round = self.sampling_history[-1]
        report["system_performance"] = {
            "coverage_efficiency": latest_round.metrics.get('coverage_efficiency', 0.0),
            "detection_accuracy": latest_round.metrics.get('detection_accuracy', 0.0),
            "path_efficiency": latest_round.metrics.get('path_efficiency', 0.0),
            "energy_efficiency": latest_round.metrics.get('energy_efficiency', 0.0)
        }

        # Health analysis
        health_values = list(latest_round.health_indices.values())
        report["health_analysis"] = {
            "average_health": float(np.mean(health_values)),
            "min_health": float(np.min(health_values)),
            "max_health": float(np.max(health_values)),
            "health_std": float(np.std(health_values)),
            "low_health_nodes": len([h for h in health_values if h < self.config.health_threshold])
        }

        # Generate recommendations
        report["recommendations"] = self._generate_recommendations(latest_round)

        return report

    def _generate_recommendations(self, latest_round: SamplingRound) -> List[str]:
        """
        生成系统建议
        Generate system recommendations
        """
        recommendations = []

        # Coverage recommendations
        coverage = latest_round.metrics.get('coverage_efficiency', 0.0)
        if coverage < 0.5:
            recommendations.append("Increase sampling coverage - current coverage is below 50%")

        # Health recommendations
        critical_alerts = sum(1 for alert in latest_round.alerts.values() if alert == 'Critical')
        if critical_alerts > 0:
            recommendations.append(f"Immediate attention required for {critical_alerts} critical health zones")

        # Feature importance recommendations
        feature_importance = latest_round.metrics.get('feature_importance', {})
        if feature_importance:
            max_importance_feature = max(feature_importance.items(), key=lambda x: x[1])
            recommendations.append(f"Monitor {max_importance_feature[0]} closely - highest impact on health")

        # Path efficiency recommendations
        path_efficiency = latest_round.metrics.get('path_efficiency', 1.0)
        if path_efficiency < 0.7:
            recommendations.append("Optimize path planning - current path efficiency is low")

        if not recommendations:
            recommendations.append("System operating within normal parameters")

        return recommendations
