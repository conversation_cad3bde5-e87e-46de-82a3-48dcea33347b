# 智能自适应采样与路径优化系统文档
# Intelligent Adaptive Sampling and Path Optimization System Documentation

## 系统概述 (System Overview)

本系统实现了一个完整的智能自适应采样解决方案，专门用于珍珠养殖池塘的水质监测。系统集成了多种先进算法，包括稀疏采样策略、图神经网络分析、自适应二次采样和多目标路径优化。

This system implements a complete intelligent adaptive sampling solution specifically designed for water quality monitoring in pearl farming ponds. The system integrates multiple advanced algorithms including sparse sampling strategies, graph neural network analysis, adaptive secondary sampling, and multi-objective path optimization.

## 核心功能 (Core Features)

### 1. 稀疏采样策略 (Sparse Sampling Strategy)
- **介数中心性算法**: 基于图论的介数中心性识别关键节点
- **多深度采样**: 在重要节点进行多层深度的环境参数采集
- **珍珠位置优先**: 优先采样珍珠养殖区域

### 2. 数据插值与健康度计算 (Data Interpolation and Health Calculation)
- **IDW插值**: 使用反距离权重插值法填充未采样节点数据
- **生物数学模型**: 基于温度、pH、电导率、叶绿素计算健康度指数
- **实时健康评估**: 动态评估池塘各区域的健康状况

### 3. 门控图神经网络分析 (Gated Graph Neural Network Analysis)
- **因果关系分析**: 使用GGNN分析环境因子对健康度的影响
- **特征重要性**: 通过GNNExplainer识别关键环境因子
- **注意力机制**: 可选的注意力机制增强模型表现

### 4. 自适应二次采样 (Adaptive Secondary Sampling)
- **低健康度区域**: 自动识别需要重点关注的区域
- **梯度变化检测**: 检测健康度梯度变化大的边界区域
- **人工干预跟踪**: 跟踪已采取措施但效果未知的区域

### 5. 多目标路径优化 (Multi-objective Path Optimization)
- **综合目标函数**: 同时优化采样覆盖率、路径长度、时间和能耗
- **约束条件**: 考虑机器人物理约束和采样能力限制
- **实时路径规划**: 基于最新信息动态调整采样路径

## 系统架构 (System Architecture)

```
┌─────────────────────────────────────────────────────────────┐
│                    IntegratedSamplingSystem                 │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Centrality      │  │ Multi-depth     │  │ IDW          │ │
│  │ Sampler         │  │ Sampler         │  │ Interpolator │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Health          │  │ GGNN            │  │ GNN          │ │
│  │ Calculator      │  │ Analyzer        │  │ Explainer    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Secondary       │  │ Path            │  │ Monitoring   │ │
│  │ Sampler         │  │ Optimizer       │  │ System       │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 文件结构 (File Structure)

```
path_optimization/
├── 智能自适应采样与路径优化数学模型.tex    # 数学模型文档
├── adaptive_sampling_system.py            # 核心算法实现
├── integrated_sampling_system.py          # 系统集成模块
├── demo_integrated_system.py              # 演示脚本
└── INTELLIGENT_SAMPLING_SYSTEM_DOCUMENTATION.md  # 本文档
```

## 使用指南 (Usage Guide)

### 快速开始 (Quick Start)

1. **安装依赖**:
```bash
pip install numpy scipy networkx torch matplotlib
```

2. **运行演示**:
```bash
python demo_integrated_system.py
```
# 一键运行完整系统
python run_complete_system.py

# 或者运行演示脚本
python demo_integrated_system.py


from adaptive_sampling_system import SamplingConfig
from integrated_sampling_system import IntegratedSamplingSystem

# 创建配置
config = SamplingConfig(
    health_threshold=0.6,
    max_sampling_capacity=50,
    depth_levels=[0.5, 1.5, 2.5]
)

# 初始化系统
system = IntegratedSamplingSystem(graph, pearls, positions, config)

# 执行采样
results = system.execute_sampling_campaign(start, end, max_rounds=3)


3. **查看结反距离权重插值使用以下公式：果**:
   - 可视化图表: `results/integrated_sampling/integrated_sampling_results.png`
   - 详细报告: `results/integrated_sampling/sampling_report.json`

### 自定义配置 (Custom Configuration)

```python
from adaptive_sampling_system import SamplingConfig
from integrated_sampling_system import IntegratedSamplingSystem

# 创建自定义配置
config = SamplingConfig(
    health_threshold=0.6,           # 健康度阈值
    gradient_threshold=0.1,         # 梯度变化阈值
    max_sampling_capacity=50,       # 最大采样容量
    max_time_budget=3600.0,         # 最大时间预算(秒)
    depth_levels=[0.5, 1.5, 2.5],   # 采样深度层级
    idw_power=2.0,                  # IDW插值幂次
    neighbor_radius=5.0             # 邻居半径
)

# 初始化系统
system = IntegratedSamplingSystem(
    pond_graph=your_graph,
    pearl_positions=your_pearl_positions,
    node_positions=your_node_positions,
    config=config
)

# 执行采样
results = system.execute_sampling_campaign(
    start_node=start_node,
    end_node=end_node,
    max_rounds=3
)
```

## 算法详解 (Algorithm Details)

### 1. 介数中心性采样 (Betweenness Centrality Sampling)

介数中心性衡量节点在网络中的重要性，计算公式为：

```
BC(v) = Σ(σ_st(v) / σ_st)
```

其中：
- `σ_st` 是从节点s到节点t的最短路径数量
- `σ_st(v)` 是通过节点v的最短路径数量

### 2. IDW插值算法 (IDW Interpolation)



```
Z(x) = Σ(w_i * Z_i) / Σ(w_i)
w_i = 1 / d_i^p
```

其中：
- `Z_i` 是已知点i的值
- `d_i` 是到已知点i的距离
- `p` 是幂次参数（默认为2）

### 3. 健康度计算模型 (Health Index Calculation)

健康度基于多个环境因子的高斯适应度函数：

```
H = Π(exp(-((x_i - μ_i)² / (2σ_i²)))^w_i)
```

其中：
- `x_i` 是环境因子i的值
- `μ_i, σ_i` 是因子i的最优值和标准差
- `w_i` 是因子i的权重

### 4. GGNN网络结构 (GGNN Architecture)

门控图神经网络使用以下更新机制：

```
u_t = σ(W_u[h_{t-1}, m_t])
r_t = σ(W_r[h_{t-1}, m_t])
h̃_t = tanh(W_h[r_t ⊙ h_{t-1}, m_t])
h_t = (1 - u_t) ⊙ h_{t-1} + u_t ⊙ h̃_t
```

## 性能指标 (Performance Metrics)

### 系统效率指标 (System Efficiency Metrics)

1. **覆盖效率**: `Coverage Efficiency = |Sampled Nodes| / |Total Nodes|`
2. **检测准确率**: `Detection Accuracy = True Positives / (True Positives + False Negatives)`
3. **路径效率**: `Path Efficiency = Direct Distance / Actual Path Length`
4. **能耗效率**: `Energy Efficiency = Useful Energy / Total Energy`

### 健康分析指标 (Health Analysis Metrics)

1. **平均健康度**: 所有节点健康度的平均值
2. **健康度标准差**: 健康度分布的离散程度
3. **低健康度节点数**: 健康度低于阈值的节点数量
4. **预警级别分布**: Critical/Warning/Normal的分布

## 输出结果 (Output Results)

### 可视化图表 (Visualization Charts)

1. **采样覆盖演化图**: 显示各轮采样的覆盖率变化
2. **健康度分布直方图**: 显示健康度的统计分布
3. **空间健康度地图**: 显示池塘各区域的健康状况
4. **系统性能指标**: 显示各项效率指标
5. **环境因子重要性**: 显示GGNN分析的特征重要性
6. **预警状态饼图**: 显示不同预警级别的分布

### 详细报告 (Detailed Reports)

系统生成JSON和文本格式的详细报告，包含：

- **活动摘要**: 采样轮数、覆盖率、节点统计
- **轮次详情**: 每轮采样的具体指标和结果
- **系统性能**: 综合性能评估
- **健康分析**: 健康度统计和分析
- **系统建议**: 基于分析结果的操作建议

## 扩展功能 (Extension Features)

### 1. 实时数据接入 (Real-time Data Integration)

系统支持接入真实传感器数据：

```python
def integrate_sensor_data(self, sensor_readings: Dict[int, np.ndarray]):
    """集成真实传感器数据"""
    # 替换模拟数据生成
    pass
```

### 2. 自定义环境因子 (Custom Environmental Factors)

可以扩展支持更多环境参数：

```python
# 在HealthIndexCalculator中添加新因子
self.optimal_params['dissolved_oxygen'] = {
    'mu': 8.0, 'sigma': 1.0, 'weight': 0.15
}
```

### 3. 高级路径约束 (Advanced Path Constraints)

支持更复杂的机器人约束：

```python
def add_robot_constraints(self, max_speed: float, battery_capacity: float):
    """添加机器人物理约束"""
    pass
```

## 故障排除 (Troubleshooting)

### 常见问题 (Common Issues)

1. **内存不足**: 对于大型池塘，考虑分块处理
2. **收敛缓慢**: 调整GGNN学习率和层数
3. **路径不优**: 增加路径优化迭代次数

### 性能优化 (Performance Optimization)

1. **并行计算**: 使用多进程处理大规模数据
2. **GPU加速**: 将GGNN计算迁移到GPU
3. **缓存机制**: 缓存中间计算结果

## 技术支持 (Technical Support)

如有技术问题，请参考：

1. **日志文件**: 系统运行日志包含详细的调试信息
2. **配置检查**: 确认所有配置参数在合理范围内
3. **数据验证**: 检查输入数据的格式和完整性

## 版本历史 (Version History)

- **v1.0.0** (2025-01-26): 初始版本，包含所有核心功能
  - 稀疏采样策略
  - GGNN分析
  - 多目标路径优化
  - 实时监测系统

## 许可证 (License)

本系统为珍珠养殖监测专用系统，版权所有。

---

**注意**: 本文档持续更新中，如有疑问请联系开发团队。
