#!/usr/bin/env python3
"""
集成智能采样系统演示
Integrated Intelligent Sampling System Demonstration

This script demonstrates the complete workflow of the intelligent adaptive sampling system
including all components: sparse sampling, GGNN analysis, adaptive secondary sampling,
and multi-objective path optimization.

Usage:
    python demo_integrated_system.py

Author: Pearl Farming Monitoring System
Date: 2025-01-26
"""

import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
import json
import os
from typing import Dict, List, Tuple
import logging

# Import our system components
from adaptive_sampling_system import SamplingConfig
from integrated_sampling_system import IntegratedSamplingSystem

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_pond_graph(length: float = 50.0, width: float = 30.0, 
                     pearl_spacing: float = 1.0, row_spacing: float = 1.5) -> Tuple[nx.Graph, List[int], Dict[int, Tuple[float, float]]]:
    """
    创建池塘图结构
    Create pond graph structure with pearl positions
    
    Returns:
        graph: NetworkX graph representing pond connectivity
        pearl_positions: List of node IDs where pearls are located
        node_positions: Dictionary mapping node IDs to (x, y) coordinates
    """
    logger.info(f"Creating pond graph: {length}m × {width}m")
    
    # Create grid of nodes
    x_coords = np.arange(0, length + pearl_spacing, pearl_spacing)
    y_coords = np.arange(0, width + row_spacing, row_spacing)
    
    graph = nx.Graph()
    node_positions = {}
    pearl_positions = []
    node_id = 0
    
    # Add nodes and positions
    for y in y_coords:
        for x in x_coords:
            graph.add_node(node_id)
            node_positions[node_id] = (x, y)
            
            # Pearl positions are at regular intervals (every other node in pearl rows)
            if 2.0 <= y <= width - 2.0 and x % 2.0 == 0:  # Pearl cultivation area
                pearl_positions.append(node_id)
            
            node_id += 1
    
    # Add edges (connect adjacent nodes)
    nodes_per_row = len(x_coords)
    for node in graph.nodes():
        x, y = node_positions[node]
        
        # Connect to right neighbor
        if (node + 1) % nodes_per_row != 0:  # Not at right edge
            graph.add_edge(node, node + 1)
        
        # Connect to bottom neighbor
        if node + nodes_per_row < len(graph.nodes()):
            graph.add_edge(node, node + nodes_per_row)
    
    logger.info(f"Graph created: {len(graph.nodes())} nodes, {len(graph.edges())} edges")
    logger.info(f"Pearl positions: {len(pearl_positions)} pearls")
    
    return graph, pearl_positions, node_positions

def visualize_sampling_results(system: IntegratedSamplingSystem, save_path: str = "results/integrated_sampling"):
    """
    可视化采样结果
    Visualize sampling results
    """
    if not system.sampling_history:
        logger.warning("No sampling data to visualize")
        return
    
    # Create results directory
    os.makedirs(save_path, exist_ok=True)
    
    # Create comprehensive visualization
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Intelligent Adaptive Sampling System Results', fontsize=16, fontweight='bold')
    
    # Get latest round data
    latest_round = system.sampling_history[-1]
    positions = system.node_positions
    
    # Plot 1: Sampling coverage evolution
    ax1 = axes[0, 0]
    rounds = [r.round_id + 1 for r in system.sampling_history]
    coverage = [r.metrics.get('coverage_efficiency', 0) for r in system.sampling_history]
    
    ax1.plot(rounds, coverage, 'bo-', linewidth=2, markersize=8)
    ax1.set_xlabel('Sampling Round')
    ax1.set_ylabel('Coverage Efficiency')
    ax1.set_title('Sampling Coverage Evolution')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 1)
    
    # Plot 2: Health index distribution
    ax2 = axes[0, 1]
    health_values = list(latest_round.health_indices.values())
    ax2.hist(health_values, bins=20, alpha=0.7, color='green', edgecolor='black')
    ax2.axvline(system.config.health_threshold, color='red', linestyle='--', 
                label=f'Threshold ({system.config.health_threshold})')
    ax2.set_xlabel('Health Index')
    ax2.set_ylabel('Frequency')
    ax2.set_title('Health Index Distribution')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Spatial health map
    ax3 = axes[0, 2]
    x_coords = [positions[node][0] for node in latest_round.health_indices.keys()]
    y_coords = [positions[node][1] for node in latest_round.health_indices.keys()]
    health_vals = list(latest_round.health_indices.values())
    
    scatter = ax3.scatter(x_coords, y_coords, c=health_vals, cmap='RdYlGn', 
                         s=30, alpha=0.8, vmin=0, vmax=1)
    
    # Highlight pearl positions
    pearl_x = [positions[p][0] for p in system.pearl_positions]
    pearl_y = [positions[p][1] for p in system.pearl_positions]
    ax3.scatter(pearl_x, pearl_y, c='blue', marker='s', s=50, alpha=0.6, label='Pearls')
    
    # Highlight sampled nodes
    sampled_x = [positions[node][0] for node in latest_round.sampled_nodes]
    sampled_y = [positions[node][1] for node in latest_round.sampled_nodes]
    ax3.scatter(sampled_x, sampled_y, c='black', marker='x', s=100, alpha=0.8, label='Sampled')
    
    ax3.set_xlabel('X Position (m)')
    ax3.set_ylabel('Y Position (m)')
    ax3.set_title('Spatial Health Distribution')
    ax3.legend()
    plt.colorbar(scatter, ax=ax3, label='Health Index')
    
    # Plot 4: Path optimization metrics
    ax4 = axes[1, 0]
    metrics_names = ['Coverage\nEfficiency', 'Path\nEfficiency', 'Energy\nEfficiency', 'Detection\nAccuracy']
    metrics_values = [
        latest_round.metrics.get('coverage_efficiency', 0),
        latest_round.metrics.get('path_efficiency', 0),
        latest_round.metrics.get('energy_efficiency', 0),
        latest_round.metrics.get('detection_accuracy', 0)
    ]
    
    bars = ax4.bar(metrics_names, metrics_values, color=['blue', 'green', 'orange', 'red'], alpha=0.7)
    ax4.set_ylabel('Efficiency Score')
    ax4.set_title('System Performance Metrics')
    ax4.set_ylim(0, 1)
    ax4.grid(True, alpha=0.3, axis='y')
    
    # Add value labels on bars
    for bar, value in zip(bars, metrics_values):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # Plot 5: Feature importance analysis
    ax5 = axes[1, 1]
    feature_importance = latest_round.metrics.get('feature_importance', {})
    
    if feature_importance:
        features = list(feature_importance.keys())
        importance_vals = list(feature_importance.values())
        
        bars = ax5.bar(features, importance_vals, color='purple', alpha=0.7)
        ax5.set_ylabel('Importance Score')
        ax5.set_title('Environmental Factor Importance (GGNN)')
        ax5.tick_params(axis='x', rotation=45)
        ax5.grid(True, alpha=0.3, axis='y')
        
        # Add value labels
        for bar, value in zip(bars, importance_vals):
            height = bar.get_height()
            ax5.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=9)
    else:
        ax5.text(0.5, 0.5, 'No GGNN Analysis\nPerformed', ha='center', va='center',
                transform=ax5.transAxes, fontsize=12)
        ax5.set_title('Environmental Factor Importance (GGNN)')
    
    # Plot 6: Alert status summary
    ax6 = axes[1, 2]
    alert_counts = {'Critical': 0, 'Warning': 0, 'Normal': 0}
    for alert in latest_round.alerts.values():
        alert_counts[alert] += 1
    
    colors = {'Critical': 'red', 'Warning': 'orange', 'Normal': 'green'}
    wedges, texts, autotexts = ax6.pie(alert_counts.values(), labels=alert_counts.keys(),
                                      colors=[colors[k] for k in alert_counts.keys()],
                                      autopct='%1.1f%%', startangle=90)
    ax6.set_title('Health Alert Status')
    
    plt.tight_layout()
    
    # Save the plot
    plot_path = os.path.join(save_path, 'integrated_sampling_results.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    logger.info(f"Visualization saved to: {plot_path}")
    
    plt.show()

def save_detailed_report(system: IntegratedSamplingSystem, save_path: str = "results/integrated_sampling"):
    """
    保存详细报告
    Save detailed system report
    """
    os.makedirs(save_path, exist_ok=True)
    
    # Generate comprehensive report
    report = system.generate_comprehensive_report()
    
    # Save as JSON
    json_path = os.path.join(save_path, 'sampling_report.json')
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # Save as readable text report
    text_path = os.path.join(save_path, 'sampling_report.txt')
    with open(text_path, 'w', encoding='utf-8') as f:
        f.write("INTELLIGENT ADAPTIVE SAMPLING SYSTEM REPORT\n")
        f.write("=" * 50 + "\n\n")
        
        # Campaign summary
        f.write("CAMPAIGN SUMMARY\n")
        f.write("-" * 20 + "\n")
        summary = report['campaign_summary']
        f.write(f"Total Rounds: {summary['total_rounds']}\n")
        f.write(f"Total Sampled Nodes: {summary['total_sampled_nodes']}\n")
        f.write(f"Total Nodes: {summary['total_nodes']}\n")
        f.write(f"Final Coverage: {summary['final_coverage']:.1%}\n\n")
        
        # System performance
        f.write("SYSTEM PERFORMANCE\n")
        f.write("-" * 20 + "\n")
        perf = report['system_performance']
        f.write(f"Coverage Efficiency: {perf['coverage_efficiency']:.3f}\n")
        f.write(f"Detection Accuracy: {perf['detection_accuracy']:.3f}\n")
        f.write(f"Path Efficiency: {perf['path_efficiency']:.3f}\n")
        f.write(f"Energy Efficiency: {perf['energy_efficiency']:.3f}\n\n")
        
        # Health analysis
        f.write("HEALTH ANALYSIS\n")
        f.write("-" * 20 + "\n")
        health = report['health_analysis']
        f.write(f"Average Health: {health['average_health']:.3f}\n")
        f.write(f"Min Health: {health['min_health']:.3f}\n")
        f.write(f"Max Health: {health['max_health']:.3f}\n")
        f.write(f"Health Std Dev: {health['health_std']:.3f}\n")
        f.write(f"Low Health Nodes: {health['low_health_nodes']}\n\n")
        
        # Recommendations
        f.write("RECOMMENDATIONS\n")
        f.write("-" * 20 + "\n")
        for i, rec in enumerate(report['recommendations'], 1):
            f.write(f"{i}. {rec}\n")
    
    logger.info(f"Detailed report saved to: {json_path} and {text_path}")

def main():
    """
    主演示函数
    Main demonstration function
    """
    logger.info("Starting Integrated Intelligent Sampling System Demonstration")
    
    # Step 1: Create pond environment
    logger.info("\n" + "="*60)
    logger.info("STEP 1: Creating Pond Environment")
    logger.info("="*60)
    
    pond_graph, pearl_positions, node_positions = create_pond_graph(
        length=50.0, width=30.0, pearl_spacing=1.0, row_spacing=1.5
    )
    
    # Step 2: Configure sampling system
    logger.info("\n" + "="*60)
    logger.info("STEP 2: Configuring Sampling System")
    logger.info("="*60)
    
    config = SamplingConfig(
        health_threshold=0.6,
        gradient_threshold=0.1,
        max_sampling_capacity=50,
        max_time_budget=3600.0,
        depth_levels=[0.5, 1.5, 2.5],
        idw_power=2.0,
        neighbor_radius=5.0
    )
    
    logger.info(f"Configuration: {config}")
    
    # Step 3: Initialize integrated system
    logger.info("\n" + "="*60)
    logger.info("STEP 3: Initializing Integrated System")
    logger.info("="*60)
    
    # Find start and end nodes (corners of the pond)
    start_node = 0  # Top-left corner
    end_node = max(node_positions.keys())  # Bottom-right corner
    
    system = IntegratedSamplingSystem(
        pond_graph=pond_graph,
        pearl_positions=pearl_positions,
        node_positions=node_positions,
        config=config
    )
    
    # Step 4: Execute sampling campaign
    logger.info("\n" + "="*60)
    logger.info("STEP 4: Executing Sampling Campaign")
    logger.info("="*60)
    
    sampling_results = system.execute_sampling_campaign(
        start_node=start_node,
        end_node=end_node,
        max_rounds=3
    )
    
    # Step 5: Generate results and reports
    logger.info("\n" + "="*60)
    logger.info("STEP 5: Generating Results and Reports")
    logger.info("="*60)
    
    # Visualize results
    visualize_sampling_results(system)
    
    # Save detailed report
    save_detailed_report(system)
    
    # Print summary
    logger.info("\n" + "="*60)
    logger.info("DEMONSTRATION COMPLETED SUCCESSFULLY")
    logger.info("="*60)
    
    final_report = system.generate_comprehensive_report()
    logger.info(f"Final Coverage: {final_report['campaign_summary']['final_coverage']:.1%}")
    logger.info(f"Total Rounds: {final_report['campaign_summary']['total_rounds']}")
    logger.info(f"Sampled Nodes: {final_report['campaign_summary']['total_sampled_nodes']}")
    
    logger.info("\nRecommendations:")
    for i, rec in enumerate(final_report['recommendations'], 1):
        logger.info(f"  {i}. {rec}")

if __name__ == "__main__":
    main()
