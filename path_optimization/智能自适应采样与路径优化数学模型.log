This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2025.1.11)  26 JUL 2025 22:36
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**智能自适应采样与路径优化数学模型
(./智能自适应采样与路径优化数学模型.tex
LaTeX2e <2024-11-01> patch level 1
L3 programming layer <2024-12-25>
(/usr/local/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2024/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count192
\c@section=\count193
\c@subsection=\count194
\c@subsubsection=\count195
\c@paragraph=\count196
\c@subparagraph=\count197
\c@figure=\count198
\c@table=\count199
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/usr/local/texlive/2024/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2024/02/08 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18


Package inputenc Warning: inputenc package ignored with utf8 based engines.

) (/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen142
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen143
) (/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count266
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count267
\leftroot@=\count268
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count269
\DOTSCASE@=\count270
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box52
\strutbox@=\box53
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen144
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count271
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count272
\dotsspace@=\muskip17
\c@parentequation=\count273
\dspbrk@lvl=\count274
\tag@help=\toks20
\row@=\count275
\column@=\count276
\maxfields@=\count277
\andhelp@=\toks21
\eqnshift@=\dimen145
\alignsep@=\dimen146
\tagshift@=\dimen147
\tagwidth@=\dimen148
\totwidth@=\dimen149
\lineht@=\dimen150
\@envbody=\toks22
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (/usr/local/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (/usr/local/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks24
) (/usr/local/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (/usr/local/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count278
\Gm@cntv=\count279
\c@Gm@tempcnt=\count280
\Gm@bindingoffset=\dimen151
\Gm@wd@mp=\dimen152
\Gm@odd@mp=\dimen153
\Gm@even@mp=\dimen154
\Gm@layoutwidth=\dimen155
\Gm@layoutheight=\dimen156
\Gm@layouthoffset=\dimen157
\Gm@layoutvoffset=\dimen158
\Gm@dimlist=\toks25
) (/usr/local/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-12-25 L3 programming layer (loader) 
 (/usr/local/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-05-08 L3 backend support: XeTeX
\g__graphics_track_int=\count281
\l__pdf_internal_box=\box54
\g__pdf_backend_annotation_int=\count282
\g__pdf_backend_link_int=\count283
))
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX
 (/usr/local/texlive/2024/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (/usr/local/texlive/2024/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate.sty
Package: xtemplate 2024-08-16 L3 Experimental prototype document functions
)
\l__xeCJK_tmp_int=\count284
\l__xeCJK_tmp_box=\box55
\l__xeCJK_tmp_dim=\dimen159
\l__xeCJK_tmp_skip=\skip54
\g__xeCJK_space_factor_int=\count285
\l__xeCJK_begin_int=\count286
\l__xeCJK_end_int=\count287
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip55
\c__xeCJK_none_node=\count288
\g__xeCJK_node_int=\count289
\c__xeCJK_CJK_node_dim=\dimen160
\c__xeCJK_CJK-space_node_dim=\dimen161
\c__xeCJK_default_node_dim=\dimen162
\c__xeCJK_CJK-widow_node_dim=\dimen163
\c__xeCJK_normalspace_node_dim=\dimen164
\c__xeCJK_default-space_node_skip=\skip56
\l__xeCJK_ccglue_skip=\skip57
\l__xeCJK_ecglue_skip=\skip58
\l__xeCJK_punct_kern_skip=\skip59
\l__xeCJK_indent_box=\box56
\l__xeCJK_last_penalty_int=\count290
\l__xeCJK_last_bound_dim=\dimen165
\l__xeCJK_last_kern_dim=\dimen166
\l__xeCJK_widow_penalty_int=\count291

LaTeX template Info: Declaring template type 'xeCJK/punctuation' taking 0
(template)           argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen167
\l__xeCJK_mixed_punct_width_dim=\dimen168
\l__xeCJK_middle_punct_width_dim=\dimen169
\l__xeCJK_fixed_margin_width_dim=\dimen170
\l__xeCJK_mixed_margin_width_dim=\dimen171
\l__xeCJK_middle_margin_width_dim=\dimen172
\l__xeCJK_bound_punct_width_dim=\dimen173
\l__xeCJK_bound_margin_width_dim=\dimen174
\l__xeCJK_margin_minimum_dim=\dimen175
\l__xeCJK_kerning_total_width_dim=\dimen176
\l__xeCJK_same_align_margin_dim=\dimen177
\l__xeCJK_different_align_margin_dim=\dimen178
\l__xeCJK_kerning_margin_width_dim=\dimen179
\l__xeCJK_kerning_margin_minimum_dim=\dimen180
\l__xeCJK_bound_dim=\dimen181
\l__xeCJK_reverse_bound_dim=\dimen182
\l__xeCJK_margin_dim=\dimen183
\l__xeCJK_minimum_bound_dim=\dimen184
\l__xeCJK_kerning_margin_dim=\dimen185
\g__xeCJK_family_int=\count292
\l__xeCJK_fam_int=\count293
\g__xeCJK_fam_allocation_int=\count294
\l__xeCJK_verb_case_int=\count295
\l__xeCJK_verb_exspace_skip=\skip60
 (/usr/local/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
)
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
 (/usr/local/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count296
\l__fontspec_language_int=\count297
\l__fontspec_strnum_int=\count298
\l__fontspec_tmp_int=\count299
\l__fontspec_tmpa_int=\count300
\l__fontspec_tmpb_int=\count301
\l__fontspec_tmpc_int=\count302
\l__fontspec_em_int=\count303
\l__fontspec_emdef_int=\count304
\l__fontspec_strong_int=\count305
\l__fontspec_strongdef_int=\count306
\l__fontspec_tmpa_dim=\dimen186
\l__fontspec_tmpb_dim=\dimen187
\l__fontspec_tmpc_dim=\dimen188
 (/usr/local/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (/usr/local/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (/usr/local/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip61
\enit@outerparindent=\dimen189
\enit@toks=\toks26
\enit@inbox=\box57
\enit@count@id=\count307
\enitdp@description=\count308
)

Package xeCJK Warning: Fandol is being set as the default font for CJK text.
(xeCJK)                Please make sure it has been properly installed.


Package fontspec Info: 
(fontspec)             Script 'CJK' not explicitly supported within font
(fontspec)             'FandolSong-Regular'. Check the typeset output, and if
(fontspec)             it is okay then ignore this warning. Otherwise a
(fontspec)             different font should be chosen.


Package fontspec Info: 
(fontspec)             Font family 'FandolSong-Regular(0)' created for font
(fontspec)             'FandolSong-Regular' with options
(fontspec)             [Script={CJK},Extension={.otf},BoldFont={FandolSong-Bold},ItalicFont={FandolKai-Regular}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[FandolSong-Regular.otf]/OT:script=hani;language=dflt;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"[FandolSong-Bold.otf]/OT:script=hani;language=dflt;"
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"[FandolKai-Regular.otf]/OT:script=hani;language=dflt;"

No file 智能自适应采样与路径优化数学模型.aux.
\openout1 = `智能自适应采样与路径优化数学模型.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 16.
LaTeX Font Info:    ... okay on input line 16.
*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


Package fontspec Info: 
(fontspec)             Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 16.
LaTeX Font Info:    Redeclaring math accent \acute on input line 16.
LaTeX Font Info:    Redeclaring math accent \grave on input line 16.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 16.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 16.
LaTeX Font Info:    Redeclaring math accent \bar on input line 16.
LaTeX Font Info:    Redeclaring math accent \breve on input line 16.
LaTeX Font Info:    Redeclaring math accent \check on input line 16.
LaTeX Font Info:    Redeclaring math accent \hat on input line 16.
LaTeX Font Info:    Redeclaring math accent \dot on input line 16.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 16.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 16.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 16.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 16.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 16.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 16.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 16.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 16.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 16.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 16.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 16.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 16.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 16.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 16.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 16.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 16.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 16.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 16.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 16.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 16.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 16.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 16.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 16.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 16.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 16.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 16.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 16.
LaTeX Font Info:    Trying to load font information for U+msa on input line 18.
(/usr/local/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 18.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[1

]
Missing character: There is no 珍 (U+73CD) in font [lmroman12-regular]:mapping=tex-text;!
Missing character: There is no 珠 (U+73E0) in font [lmroman12-regular]:mapping=tex-text;!
Missing character: There is no 位 (U+4F4D) in font [lmroman12-regular]:mapping=tex-text;!
Missing character: There is no 置 (U+7F6E) in font [lmroman12-regular]:mapping=tex-text;!
Missing character: There is no 节 (U+8282) in font [lmroman12-regular]:mapping=tex-text;!
Missing character: There is no 点 (U+70B9) in font [lmroman12-regular]:mapping=tex-text;!


[2]

[3]

[4]

[5]

[6] (./智能自适应采样与路径优化数学模型.aux)
 ***********
LaTeX2e <2024-11-01> patch level 1
L3 programming layer <2022/08/05>
 ***********
 ) 
Here is how much of TeX's memory you used:
 6781 strings out of 473853
 185213 string characters out of 5730238
 609648 words of memory out of 5000000
 29788 multiletter control sequences out of 15000+600000
 566839 words of font info for 92 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 74i,11n,91p,299b,304s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on 智能自适应采样与路径优化数学模型.pdf (6 pages).
