#!/usr/bin/env python3
"""
快速Mask分析测试
Quick Mask Analysis Test

This script provides a quick test of mask analysis with fewer epochs for faster results.
"""

import sys
import os
import time
import logging
from pathlib import Path
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from typing import Dict, List, Tuple, Optional
import json

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from adaptive_sampling_system import SamplingConfig, GatedGraphNeuralNetwork

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickMaskAnalyzer:
    """快速Mask分析器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
        self.mask_results = {}
        
    def generate_masked_data(self, num_samples: int = 200, num_nodes: int = 10, 
                           mask_ratio: float = 0.0) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """生成带mask的训练数据"""
        # Generate base features (temperature, pH, conductivity, chlorophyll)
        features = torch.randn(num_samples, num_nodes, 4)
        
        # Normalize features to realistic ranges
        features[:, :, 0] = features[:, :, 0] * 5 + 25  # Temperature: 20-30°C
        features[:, :, 1] = features[:, :, 1] * 1 + 7.5  # pH: 6.5-8.5
        features[:, :, 2] = features[:, :, 2] * 200 + 500  # Conductivity: 300-700 μS/cm
        features[:, :, 3] = torch.abs(features[:, :, 3]) * 10 + 5  # Chlorophyll: 5-15 μg/L
        
        # Generate adjacency matrices (fully connected for simplicity)
        adj_matrices = torch.ones(num_samples, num_nodes, num_nodes)
        # Remove self-loops
        for i in range(num_nodes):
            adj_matrices[:, i, i] = 0
        
        # Generate health targets based on environmental factors
        temp_factor = 1.0 - torch.abs(features[:, :, 0] - 27.5) / 10  # Optimal at 27.5°C
        ph_factor = 1.0 - torch.abs(features[:, :, 1] - 7.5) / 2     # Optimal at pH 7.5
        cond_factor = 1.0 - torch.abs(features[:, :, 2] - 500) / 300  # Optimal at 500 μS/cm
        chlor_factor = torch.clamp(features[:, :, 3] / 15, 0, 1)      # Higher is better
        
        health_targets = (temp_factor + ph_factor + cond_factor + chlor_factor) / 4
        health_targets = torch.clamp(health_targets, 0, 1)
        
        # Apply mask to features
        if mask_ratio > 0:
            mask = torch.rand_like(features) > mask_ratio
            features = features * mask.float()
        
        return features.to(self.device), adj_matrices.to(self.device), health_targets.to(self.device)
    
    def train_with_mask(self, mask_ratio: float, epochs: int = 50) -> Dict:
        """使用指定mask比例训练模型"""
        logger.info(f"Training with mask ratio: {mask_ratio:.1f}")
        
        # Create model
        model = GatedGraphNeuralNetwork(
            input_dim=4,
            hidden_dim=32,  # Smaller for faster training
            output_dim=1,
            num_layers=2,   # Fewer layers for faster training
            use_attention=True
        ).to(self.device)
        
        # Setup optimizer and loss
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
        criterion = nn.MSELoss()
        
        # Generate training and validation data (smaller datasets)
        train_features, train_adj, train_targets = self.generate_masked_data(160, 10, mask_ratio)
        val_features, val_adj, val_targets = self.generate_masked_data(40, 10, mask_ratio)
        
        # Training metrics
        train_mses = []
        val_mses = []
        
        model.train()
        for epoch in range(epochs):
            # Training phase
            optimizer.zero_grad()
            train_outputs = model(train_features, train_adj)
            train_loss = criterion(train_outputs.squeeze(), train_targets)
            train_loss.backward()
            optimizer.step()
            
            # Validation phase
            model.eval()
            with torch.no_grad():
                val_outputs = model(val_features, val_adj)
                
                # Calculate MSE
                train_mse = torch.mean((train_outputs.squeeze() - train_targets) ** 2).item()
                val_mse = torch.mean((val_outputs.squeeze() - val_targets) ** 2).item()
            
            model.train()
            
            # Store metrics
            train_mses.append(train_mse)
            val_mses.append(val_mse)
            
            if (epoch + 1) % 5 == 0:
                logger.info(f"Mask {mask_ratio:.1f} - Epoch {epoch+1}/{epochs}: "
                          f"Train MSE: {train_mse:.6f}, Val MSE: {val_mse:.6f}")
        
        return {
            'mask_ratio': mask_ratio,
            'train_mses': train_mses,
            'val_mses': val_mses,
            'final_train_mse': train_mses[-1],
            'final_val_mse': val_mses[-1]
        }
    
    def run_analysis(self, mask_ratios: List[float] = None, epochs: int = 50):
        """运行完整的mask分析"""
        if mask_ratios is None:
            mask_ratios = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        logger.info(f"Starting quick mask analysis with ratios: {mask_ratios}")
        logger.info(f"Training epochs per mask: {epochs}")
        
        start_time = time.time()
        
        for mask_ratio in mask_ratios:
            result = self.train_with_mask(mask_ratio, epochs)
            self.mask_results[mask_ratio] = result
            
            logger.info(f"Completed mask {mask_ratio:.1f}: "
                       f"Final Val MSE = {result['final_val_mse']:.6f}")
        
        total_time = time.time() - start_time
        logger.info(f"Quick mask analysis completed in {total_time:.2f} seconds")
        
        return self.mask_results
    
    def visualize_results(self, save_path: str = "results/mask_analysis"):
        """可视化mask分析结果 - 所有mask比例的训练曲线在一张图"""
        if not self.mask_results:
            logger.warning("No mask analysis results to visualize")
            return

        # Create results directory
        os.makedirs(save_path, exist_ok=True)

        # Create single figure for all training curves
        plt.figure(figsize=(14, 10))

        # Define colors for different mask ratios
        colors = plt.cm.viridis(np.linspace(0, 1, len(self.mask_results)))

        # Extract data for plotting
        mask_ratios = sorted(self.mask_results.keys())

        # Plot training curves for all mask ratios
        for i, mask_ratio in enumerate(mask_ratios):
            result = self.mask_results[mask_ratio]
            epochs = range(1, len(result['val_mses']) + 1)

            # Plot validation MSE curves (more important for comparison)
            plt.plot(epochs, result['val_mses'],
                    color=colors[i], linewidth=2.5,
                    label=f'Mask = {mask_ratio:.1f}',
                    marker='o' if mask_ratio in [0.0, 0.5, 0.9] else None,
                    markersize=4, markevery=5)

        plt.xlabel('Epoch', fontsize=14)
        plt.ylabel('Validation MSE', fontsize=14)
        plt.title('GGNN Training Curves for Different Mask Ratios', fontsize=16, fontweight='bold')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=11)
        plt.grid(True, alpha=0.3)
        plt.yscale('log')

        # Add text box with summary statistics
        final_val_mses = [self.mask_results[mr]['final_val_mse'] for mr in mask_ratios]
        baseline_mse = final_val_mses[0]
        worst_mse = max(final_val_mses)

        summary_text = f"""Summary Statistics:
Baseline MSE (No Mask): {baseline_mse:.6f}
Worst MSE (Mask=0.9): {worst_mse:.6f}
Performance Ratio: {worst_mse/baseline_mse:.2f}x
Training Epochs: {len(result['val_mses'])}"""

        plt.text(0.02, 0.98, summary_text, transform=plt.gca().transAxes,
                fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))

        plt.tight_layout()

        # Save the plot
        plot_path = os.path.join(save_path, 'mask_training_curves.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        logger.info(f"Mask training curves saved to: {plot_path}")

        plt.show()

        # Print detailed summary
        print("\n" + "="*70)
        print("MASK RATIO ANALYSIS - TRAINING CURVES SUMMARY")
        print("="*70)
        print(f"{'Mask Ratio':<12} {'Final Val MSE':<15} {'Degradation':<12} {'Status'}")
        print("-"*70)

        for mask_ratio in mask_ratios:
            final_mse = self.mask_results[mask_ratio]['final_val_mse']
            degradation = (final_mse / baseline_mse - 1) * 100

            if degradation < 50:
                status = "Good"
            elif degradation < 100:
                status = "Moderate"
            else:
                status = "Poor"

            print(f"{mask_ratio:<12.1f} {final_mse:<15.6f} {degradation:<12.1f}% {status}")

        print("="*70)

        return plot_path

def main():
    """主函数"""
    print("🎯 Quick GGNN Model Mask Ratio Analysis")
    print("=" * 50)
    
    # Create analyzer
    analyzer = QuickMaskAnalyzer()
    
    # Define mask ratios to test
    mask_ratios = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    
    print(f"📊 Testing mask ratios: {mask_ratios}")
    print(f"🔄 Training epochs per mask: 50")

    # Run analysis
    print("\n🚀 Starting mask analysis...")
    results = analyzer.run_analysis(mask_ratios, 50)
    
    # Visualize results
    print("\n📈 Generating visualizations...")
    plot_path = analyzer.visualize_results()
    
    print(f"\n✅ Quick analysis completed!")
    print(f"📊 Visualization: {plot_path}")
    
    return results

if __name__ == "__main__":
    main()
