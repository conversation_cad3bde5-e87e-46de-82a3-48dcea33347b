#!/usr/bin/env python3
"""
完整系统运行脚本
Complete System Execution Script

This script provides a comprehensive demonstration of the intelligent adaptive sampling system
with all components integrated and working together.

Features demonstrated:
1. Sparse sampling based on betweenness centrality
2. Multi-depth sampling at critical nodes
3. IDW interpolation for unsampled nodes
4. GGNN analysis with feature importance
5. Adaptive secondary sampling
6. Multi-objective path optimization
7. Real-time monitoring and alerts

Usage:
    python run_complete_system.py

Author: Pearl Farming Monitoring System
Date: 2025-01-26
"""

import sys
import os
import time
import logging
from pathlib import Path

# Add current directory to path for imports
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

try:
    from demo_integrated_system import main as run_demo
    print("✓ Successfully imported demo system")
except ImportError as e:
    print(f"✗ Import error: {e}")
    print("Please ensure all required files are in the same directory:")
    print("  - adaptive_sampling_system.py")
    print("  - integrated_sampling_system.py") 
    print("  - demo_integrated_system.py")
    sys.exit(1)

def check_dependencies():
    """检查系统依赖"""
    print("Checking system dependencies...")
    
    required_packages = [
        'numpy', 'scipy', 'networkx', 'torch', 'matplotlib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Please install them using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✓ All dependencies satisfied")
    return True

def create_results_directory():
    """创建结果目录"""
    results_dir = Path("results/integrated_sampling")
    results_dir.mkdir(parents=True, exist_ok=True)
    print(f"✓ Results directory created: {results_dir}")
    return results_dir

def print_system_info():
    """打印系统信息"""
    print("\n" + "="*80)
    print("INTELLIGENT ADAPTIVE SAMPLING SYSTEM")
    print("智能自适应采样与路径优化系统")
    print("="*80)
    print()
    print("System Components:")
    print("  1. Sparse Sampling Strategy (稀疏采样策略)")
    print("     - Betweenness centrality node selection")
    print("     - Multi-depth sampling at critical points")
    print()
    print("  2. Data Interpolation & Health Calculation (数据插值与健康度计算)")
    print("     - IDW interpolation for unsampled nodes")
    print("     - Biological mathematical health index model")
    print()
    print("  3. GGNN Analysis (门控图神经网络分析)")
    print("     - Causal relationship analysis")
    print("     - Feature importance identification")
    print("     - Optional attention mechanism")
    print()
    print("  4. Adaptive Secondary Sampling (自适应二次采样)")
    print("     - Low health region identification")
    print("     - Gradient boundary detection")
    print("     - Intervention area tracking")
    print()
    print("  5. Multi-objective Path Optimization (多目标路径优化)")
    print("     - Coverage, distance, time, energy optimization")
    print("     - Robot constraint satisfaction")
    print("     - Real-time path replanning")
    print()
    print("  6. Real-time Monitoring & Alerts (实时监测与预警)")
    print("     - Health status evaluation")
    print("     - Performance metrics calculation")
    print("     - Automated recommendations")
    print()

def print_execution_summary():
    """打印执行摘要"""
    print("\n" + "="*80)
    print("EXECUTION SUMMARY")
    print("="*80)
    print()
    print("The system will execute the following workflow:")
    print()
    print("Phase 1: Environment Setup")
    print("  - Create 50m × 30m pond graph structure")
    print("  - Generate pearl positions with 1m spacing")
    print("  - Establish 1.5m row spacing")
    print("  - Initialize robot navigation constraints")
    print()
    print("Phase 2: Initial Sparse Sampling")
    print("  - Calculate betweenness centrality for all nodes")
    print("  - Select high-importance nodes (80th percentile)")
    print("  - Prioritize pearl cultivation areas")
    print("  - Perform multi-depth sampling")
    print()
    print("Phase 3: Data Analysis & Interpolation")
    print("  - Apply IDW interpolation to unsampled nodes")
    print("  - Calculate health indices using biological model")
    print("  - Identify low-health regions (< 0.6 threshold)")
    print()
    print("Phase 4: GGNN Causal Analysis")
    print("  - Analyze environmental factor relationships")
    print("  - Calculate feature importance weights")
    print("  - Generate causal insights")
    print()
    print("Phase 5: Adaptive Secondary Sampling")
    print("  - Identify secondary sampling targets")
    print("  - Calculate health gradients")
    print("  - Plan additional sampling rounds")
    print()
    print("Phase 6: Path Optimization & Execution")
    print("  - Multi-objective path planning")
    print("  - Robot constraint satisfaction")
    print("  - Real-time path adjustment")
    print()
    print("Phase 7: Results & Reporting")
    print("  - Generate comprehensive visualizations")
    print("  - Create detailed performance reports")
    print("  - Provide operational recommendations")
    print()

def main():
    """主执行函数"""
    print("Starting Complete System Execution...")
    print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Print system information
    print_system_info()
    
    # Check dependencies
    print("\n" + "-"*60)
    print("DEPENDENCY CHECK")
    print("-"*60)
    if not check_dependencies():
        return False
    
    # Create results directory
    print("\n" + "-"*60)
    print("SETUP")
    print("-"*60)
    results_dir = create_results_directory()
    
    # Print execution summary
    print_execution_summary()
    
    # Confirm execution
    print("\n" + "-"*60)
    print("READY TO EXECUTE")
    print("-"*60)
    print("The system is ready to run the complete demonstration.")
    print("This will take approximately 2-3 minutes to complete.")
    print()
    
    response = input("Do you want to proceed? (y/n): ").lower().strip()
    if response not in ['y', 'yes']:
        print("Execution cancelled by user.")
        return False
    
    # Execute the demonstration
    print("\n" + "="*80)
    print("EXECUTING INTELLIGENT SAMPLING SYSTEM")
    print("="*80)
    
    start_time = time.time()
    
    try:
        # Run the complete demonstration
        run_demo()
        
        execution_time = time.time() - start_time
        
        print("\n" + "="*80)
        print("EXECUTION COMPLETED SUCCESSFULLY")
        print("="*80)
        print(f"Total execution time: {execution_time:.2f} seconds")
        print()
        print("Generated outputs:")
        print(f"  - Visualization: {results_dir}/integrated_sampling_results.png")
        print(f"  - JSON Report: {results_dir}/sampling_report.json")
        print(f"  - Text Report: {results_dir}/sampling_report.txt")
        print()
        print("Key achievements:")
        print("  ✓ Sparse sampling strategy implemented")
        print("  ✓ GGNN analysis completed")
        print("  ✓ Adaptive secondary sampling executed")
        print("  ✓ Multi-objective path optimization performed")
        print("  ✓ Real-time monitoring and alerts generated")
        print("  ✓ Comprehensive reports created")
        print()
        print("Next steps:")
        print("  1. Review the generated visualizations")
        print("  2. Analyze the detailed reports")
        print("  3. Implement recommendations")
        print("  4. Integrate with real sensor data")
        print("  5. Deploy to production environment")
        
        return True
        
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"\n✗ Execution failed after {execution_time:.2f} seconds")
        print(f"Error: {str(e)}")
        print("\nTroubleshooting tips:")
        print("  1. Check that all required files are present")
        print("  2. Verify Python package installations")
        print("  3. Ensure sufficient memory is available")
        print("  4. Check file permissions for results directory")
        
        # Print detailed error information
        import traceback
        print("\nDetailed error information:")
        print("-" * 40)
        traceback.print_exc()
        
        return False

def print_usage_instructions():
    """打印使用说明"""
    print("\n" + "="*80)
    print("USAGE INSTRUCTIONS")
    print("="*80)
    print()
    print("Command Line Usage:")
    print("  python run_complete_system.py")
    print()
    print("Required Files:")
    print("  - adaptive_sampling_system.py")
    print("  - integrated_sampling_system.py")
    print("  - demo_integrated_system.py")
    print("  - run_complete_system.py (this file)")
    print()
    print("Required Python Packages:")
    print("  - numpy (numerical computing)")
    print("  - scipy (scientific computing)")
    print("  - networkx (graph algorithms)")
    print("  - torch (neural networks)")
    print("  - matplotlib (visualization)")
    print()
    print("Output Files:")
    print("  - results/integrated_sampling/integrated_sampling_results.png")
    print("  - results/integrated_sampling/sampling_report.json")
    print("  - results/integrated_sampling/sampling_report.txt")
    print()
    print("System Requirements:")
    print("  - Python 3.7 or higher")
    print("  - At least 4GB RAM")
    print("  - 100MB free disk space")
    print()

if __name__ == "__main__":
    # Check if help is requested
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print_usage_instructions()
        sys.exit(0)
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('system_execution.log')
        ]
    )
    
    # Execute main function
    success = main()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
