#!/usr/bin/env python3
"""
完整演示脚本 - 包含训练过程
Complete Demo Script with Training Process

This script demonstrates the complete workflow including:
1. GGNN model training with loss/MAE curves
2. Intelligent adaptive sampling system
3. Comprehensive visualization and analysis
4. Training metrics and convergence analysis

Usage:
    python complete_demo_with_training.py

Author: Pearl Farming Monitoring System
Date: 2025-01-26
"""

import sys
import os
import time
import logging
from pathlib import Path
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from adaptive_sampling_system import SamplingConfig, GatedGraphNeuralNetwork
from integrated_sampling_system import IntegratedSamplingSystem
from enhanced_training_system import EnhancedGGNNTrainer
from demo_integrated_system import create_pond_graph, visualize_sampling_results, save_detailed_report

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_enhanced_visualization(system: IntegratedSamplingSystem, trainer: EnhancedGGNNTrainer, 
                                save_path: str = "results/complete_analysis"):
    """创建增强的可视化分析"""
    Path(save_path).mkdir(parents=True, exist_ok=True)
    
    # Create comprehensive analysis figure
    fig = plt.figure(figsize=(20, 16))
    gs = fig.add_gridspec(4, 4, hspace=0.3, wspace=0.3)
    
    fig.suptitle('Complete Intelligent Sampling System Analysis\n智能采样系统完整分析', 
                fontsize=18, fontweight='bold')
    
    if not system.sampling_history:
        logger.warning("No sampling data available for visualization")
        return
    
    latest_round = system.sampling_history[-1]
    positions = system.node_positions
    
    # 1. Training Loss Curves (Top Left)
    ax1 = fig.add_subplot(gs[0, :2])
    epochs = trainer.metrics.epochs
    ax1.plot(epochs, trainer.metrics.train_losses, 'b-', label='Training Loss', linewidth=2)
    ax1.plot(epochs, trainer.metrics.val_losses, 'r-', label='Validation Loss', linewidth=2)
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss (MSE)')
    ax1.set_title('GGNN Training Loss Curves')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_yscale('log')
    
    # 2. MAE Curves (Top Right)
    ax2 = fig.add_subplot(gs[0, 2:])
    ax2.plot(epochs, trainer.metrics.train_mae, 'b-', label='Training MAE', linewidth=2)
    ax2.plot(epochs, trainer.metrics.val_mae, 'r-', label='Validation MAE', linewidth=2)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Mean Absolute Error')
    ax2.set_title('GGNN Training MAE Curves')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. Spatial Health Distribution (Second Row Left)
    ax3 = fig.add_subplot(gs[1, :2])
    x_coords = [positions[node][0] for node in latest_round.health_indices.keys()]
    y_coords = [positions[node][1] for node in latest_round.health_indices.keys()]
    health_vals = list(latest_round.health_indices.values())
    
    scatter = ax3.scatter(x_coords, y_coords, c=health_vals, cmap='RdYlGn', 
                         s=40, alpha=0.8, vmin=0, vmax=1)
    
    # Highlight pearl positions
    pearl_x = [positions[p][0] for p in system.pearl_positions]
    pearl_y = [positions[p][1] for p in system.pearl_positions]
    ax3.scatter(pearl_x, pearl_y, c='blue', marker='s', s=60, alpha=0.7, label='Pearls')
    
    # Highlight sampled nodes
    sampled_x = [positions[node][0] for node in latest_round.sampled_nodes]
    sampled_y = [positions[node][1] for node in latest_round.sampled_nodes]
    ax3.scatter(sampled_x, sampled_y, c='black', marker='x', s=120, alpha=0.9, label='Sampled')
    
    ax3.set_xlabel('X Position (m)')
    ax3.set_ylabel('Y Position (m)')
    ax3.set_title('Spatial Health Distribution with Sampling Points')
    ax3.legend()
    plt.colorbar(scatter, ax=ax3, label='Health Index', shrink=0.8)
    
    # 4. Sampling Coverage Evolution (Second Row Right)
    ax4 = fig.add_subplot(gs[1, 2:])
    rounds = [r.round_id + 1 for r in system.sampling_history]
    coverage = [r.metrics.get('coverage_efficiency', 0) for r in system.sampling_history]
    cumulative_nodes = [len(r.sampled_nodes) for r in system.sampling_history]
    
    ax4_twin = ax4.twinx()
    
    line1 = ax4.plot(rounds, coverage, 'bo-', linewidth=3, markersize=8, label='Coverage Efficiency')
    line2 = ax4_twin.plot(rounds, cumulative_nodes, 'ro-', linewidth=3, markersize=8, label='Sampled Nodes')
    
    ax4.set_xlabel('Sampling Round')
    ax4.set_ylabel('Coverage Efficiency', color='blue')
    ax4_twin.set_ylabel('Number of Sampled Nodes', color='red')
    ax4.set_title('Sampling Progress Evolution')
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim(0, 1)
    
    # Combine legends
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax4.legend(lines, labels, loc='center right')
    
    # 5. Health Index Distribution (Third Row Left)
    ax5 = fig.add_subplot(gs[2, 0])
    health_values = list(latest_round.health_indices.values())
    ax5.hist(health_values, bins=20, alpha=0.7, color='green', edgecolor='black')
    ax5.axvline(system.config.health_threshold, color='red', linestyle='--', linewidth=2,
                label=f'Threshold ({system.config.health_threshold})')
    ax5.set_xlabel('Health Index')
    ax5.set_ylabel('Frequency')
    ax5.set_title('Health Index Distribution')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. System Performance Metrics (Third Row Center-Left)
    ax6 = fig.add_subplot(gs[2, 1])
    metrics_names = ['Coverage', 'Path Eff.', 'Energy Eff.', 'Detection']
    metrics_values = [
        latest_round.metrics.get('coverage_efficiency', 0),
        latest_round.metrics.get('path_efficiency', 0),
        latest_round.metrics.get('energy_efficiency', 0),
        latest_round.metrics.get('detection_accuracy', 0)
    ]
    
    bars = ax6.bar(metrics_names, metrics_values, 
                   color=['blue', 'green', 'orange', 'red'], alpha=0.7)
    ax6.set_ylabel('Efficiency Score')
    ax6.set_title('System Performance')
    ax6.set_ylim(0, 1)
    ax6.grid(True, alpha=0.3, axis='y')
    
    # Add value labels
    for bar, value in zip(bars, metrics_values):
        height = bar.get_height()
        ax6.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=9)
    
    # 7. Feature Importance (Third Row Center-Right)
    ax7 = fig.add_subplot(gs[2, 2])
    feature_importance = latest_round.metrics.get('feature_importance', {})
    
    if feature_importance:
        features = list(feature_importance.keys())
        importance_vals = list(feature_importance.values())
        
        bars = ax7.bar(features, importance_vals, color='purple', alpha=0.7)
        ax7.set_ylabel('Importance Score')
        ax7.set_title('Environmental Factor Importance')
        ax7.tick_params(axis='x', rotation=45)
        ax7.grid(True, alpha=0.3, axis='y')
        
        # Add value labels
        for bar, value in zip(bars, importance_vals):
            height = bar.get_height()
            ax7.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                    f'{value:.3f}', ha='center', va='bottom', fontsize=8)
    else:
        ax7.text(0.5, 0.5, 'No GGNN\nAnalysis', ha='center', va='center',
                transform=ax7.transAxes, fontsize=12)
        ax7.set_title('Environmental Factor Importance')
    
    # 8. Alert Status (Third Row Right)
    ax8 = fig.add_subplot(gs[2, 3])
    alert_counts = {'Critical': 0, 'Warning': 0, 'Normal': 0}
    for alert in latest_round.alerts.values():
        alert_counts[alert] += 1
    
    colors = {'Critical': 'red', 'Warning': 'orange', 'Normal': 'green'}
    wedges, texts, autotexts = ax8.pie(alert_counts.values(), labels=alert_counts.keys(),
                                      colors=[colors[k] for k in alert_counts.keys()],
                                      autopct='%1.1f%%', startangle=90)
    ax8.set_title('Health Alert Status')
    
    # 9. Convergence Analysis (Bottom Row Left)
    ax9 = fig.add_subplot(gs[3, :2])
    if len(trainer.metrics.convergence_metrics) > 0:
        # Ensure arrays have same length
        conv_epochs = epochs[:len(trainer.metrics.convergence_metrics)]
        ax9.plot(conv_epochs, trainer.metrics.convergence_metrics, 'purple', linewidth=2)
        ax9.axhline(y=0.01, color='red', linestyle='--', linewidth=2, label='Convergence Threshold')
        ax9.set_xlabel('Epoch')
        ax9.set_ylabel('Relative Change in Val Loss')
        ax9.set_title('Training Convergence Analysis')
        ax9.legend()
        ax9.grid(True, alpha=0.3)
        ax9.set_yscale('log')
    
    # 10. Learning Rate Schedule (Bottom Row Right)
    ax10 = fig.add_subplot(gs[3, 2:])
    ax10.plot(epochs, trainer.metrics.learning_rates, 'g-', linewidth=2)
    ax10.set_xlabel('Epoch')
    ax10.set_ylabel('Learning Rate')
    ax10.set_title('Learning Rate Schedule')
    ax10.grid(True, alpha=0.3)
    ax10.set_yscale('log')
    
    # Save the comprehensive plot
    plot_path = Path(save_path) / 'complete_system_analysis.png'
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    logger.info(f"Complete analysis visualization saved to: {plot_path}")
    
    plt.show()
    
    return plot_path

def print_comprehensive_summary(system: IntegratedSamplingSystem, trainer: EnhancedGGNNTrainer, 
                              training_summary: dict):
    """打印综合摘要"""
    print("\n" + "="*80)
    print("COMPREHENSIVE SYSTEM EXECUTION SUMMARY")
    print("综合系统执行摘要")
    print("="*80)
    
    # Training Summary
    print("\n📊 GGNN TRAINING RESULTS")
    print("-" * 40)
    print(f"Total Training Epochs: {training_summary['total_epochs']}")
    print(f"Best Validation Loss: {training_summary['best_val_loss']:.6f}")
    print(f"Final Training Loss: {training_summary['final_train_loss']:.6f}")
    print(f"Final Validation Loss: {training_summary['final_val_loss']:.6f}")
    print(f"Final Training MAE: {training_summary['final_train_mae']:.6f}")
    print(f"Final Validation MAE: {training_summary['final_val_mae']:.6f}")
    print(f"Convergence Achieved: {'Yes' if training_summary['convergence_achieved'] else 'No'}")
    
    # Sampling Summary
    final_report = system.generate_comprehensive_report()
    print("\n🎯 SAMPLING SYSTEM RESULTS")
    print("-" * 40)
    print(f"Total Sampling Rounds: {final_report['campaign_summary']['total_rounds']}")
    print(f"Final Coverage: {final_report['campaign_summary']['final_coverage']:.1%}")
    print(f"Total Sampled Nodes: {final_report['campaign_summary']['total_sampled_nodes']}")
    print(f"Total Available Nodes: {final_report['campaign_summary']['total_nodes']}")
    
    # Performance Metrics
    print("\n⚡ SYSTEM PERFORMANCE")
    print("-" * 40)
    perf = final_report['system_performance']
    print(f"Coverage Efficiency: {perf['coverage_efficiency']:.3f}")
    print(f"Detection Accuracy: {perf['detection_accuracy']:.3f}")
    print(f"Path Efficiency: {perf['path_efficiency']:.3f}")
    print(f"Energy Efficiency: {perf['energy_efficiency']:.3f}")
    
    # Health Analysis
    print("\n🏥 HEALTH ANALYSIS")
    print("-" * 40)
    health = final_report['health_analysis']
    print(f"Average Health Index: {health['average_health']:.3f}")
    print(f"Health Index Range: {health['min_health']:.3f} - {health['max_health']:.3f}")
    print(f"Health Standard Deviation: {health['health_std']:.3f}")
    print(f"Low Health Nodes: {health['low_health_nodes']}")
    
    # Recommendations
    print("\n💡 SYSTEM RECOMMENDATIONS")
    print("-" * 40)
    for i, rec in enumerate(final_report['recommendations'], 1):
        print(f"{i}. {rec}")
    
    print("\n" + "="*80)

def main():
    """主函数 - 完整演示流程"""
    print("Starting Complete Demo with Training Process...")
    print("开始完整演示流程（包含训练过程）...")
    
    start_time = time.time()
    
    # Step 1: Create pond environment
    logger.info("\n🌊 STEP 1: Creating Pond Environment")
    pond_graph, pearl_positions, node_positions = create_pond_graph(
        length=50.0, width=30.0, pearl_spacing=1.0, row_spacing=1.5
    )
    
    # Step 2: Configure system
    logger.info("\n⚙️ STEP 2: Configuring System")
    config = SamplingConfig(
        health_threshold=0.6,
        gradient_threshold=0.1,
        max_sampling_capacity=50,
        max_time_budget=3600.0,
        depth_levels=[0.5, 1.5, 2.5],
        idw_power=2.0,
        neighbor_radius=5.0
    )
    
    # Step 3: Initialize and train GGNN
    logger.info("\n🧠 STEP 3: Training GGNN Model")
    ggnn_model = GatedGraphNeuralNetwork(
        input_dim=4, hidden_dim=64, output_dim=1, num_layers=3, use_attention=True
    )
    
    trainer = EnhancedGGNNTrainer(ggnn_model, config)
    training_summary = trainer.train_model(num_epochs=50, train_ratio=0.8)
    
    # Step 4: Initialize integrated system with trained model
    logger.info("\n🔧 STEP 4: Initializing Integrated System")
    start_node = 0
    end_node = max(node_positions.keys())
    
    system = IntegratedSamplingSystem(
        pond_graph=pond_graph,
        pearl_positions=pearl_positions,
        node_positions=node_positions,
        config=config
    )
    
    # Replace the system's GGNN with our trained model
    system.ggnn = trainer.model
    
    # Step 5: Execute sampling campaign
    logger.info("\n🎯 STEP 5: Executing Sampling Campaign")
    sampling_results = system.execute_sampling_campaign(
        start_node=start_node,
        end_node=end_node,
        max_rounds=3
    )
    
    # Step 6: Generate comprehensive visualizations
    logger.info("\n📊 STEP 6: Generating Comprehensive Analysis")
    
    # Plot training curves
    trainer.plot_training_curves()
    trainer.save_training_metrics()
    
    # Create enhanced visualization
    create_enhanced_visualization(system, trainer)
    
    # Save detailed reports
    save_detailed_report(system)
    
    # Step 7: Print comprehensive summary
    logger.info("\n📋 STEP 7: Generating Summary Report")
    print_comprehensive_summary(system, trainer, training_summary)
    
    total_time = time.time() - start_time
    
    print(f"\n✅ COMPLETE DEMO FINISHED SUCCESSFULLY!")
    print(f"Total execution time: {total_time:.2f} seconds")
    print(f"Generated outputs in: results/")
    print("  - Training curves: results/training_curves/")
    print("  - Complete analysis: results/complete_analysis/")
    print("  - Sampling reports: results/integrated_sampling/")

if __name__ == "__main__":
    main()
