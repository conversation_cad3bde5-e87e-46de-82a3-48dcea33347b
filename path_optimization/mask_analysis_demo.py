#!/usr/bin/env python3
"""
Mask Ratio Analysis for GGNN Model
不同Mask比例下的MSE分析

This script analyzes GGNN model performance under different mask ratios
to evaluate robustness to missing data.

Features:
1. Train GGNN models with different mask ratios (0.0 to 0.9)
2. Generate MSE curves for each mask ratio
3. Visualize performance degradation with increasing mask ratios
4. Save comprehensive analysis results

Usage:
    python mask_analysis_demo.py

Author: Pearl Farming Monitoring System
Date: 2025-01-26
"""

import sys
import os
import time
import logging
from pathlib import Path
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from typing import Dict, List, Tuple, Optional
import json

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from adaptive_sampling_system import SamplingConfig, GatedGraphNeuralNetwork
from enhanced_training_system import EnhancedGGNNTrainer

class TrainingConfig:
    """训练配置类"""
    def __init__(self, learning_rate=0.001, batch_size=32, epochs=50,
                 weight_decay=1e-5, dropout=0.1):
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.epochs = epochs
        self.weight_decay = weight_decay
        self.dropout = dropout

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MaskAnalysisTrainer:
    """
    Mask分析训练器
    Trainer for analyzing model performance under different mask ratios
    """
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
        
        # Results storage
        self.mask_results = {}
        
    def generate_masked_data(self, num_samples: int = 1000, num_nodes: int = 20, 
                           mask_ratio: float = 0.0) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        生成带mask的训练数据
        Generate training data with specified mask ratio
        """
        # Generate base features (temperature, pH, conductivity, chlorophyll)
        features = torch.randn(num_samples, num_nodes, 4)
        
        # Normalize features to realistic ranges
        features[:, :, 0] = features[:, :, 0] * 5 + 25  # Temperature: 20-30°C
        features[:, :, 1] = features[:, :, 1] * 1 + 7.5  # pH: 6.5-8.5
        features[:, :, 2] = features[:, :, 2] * 200 + 500  # Conductivity: 300-700 μS/cm
        features[:, :, 3] = torch.abs(features[:, :, 3]) * 10 + 5  # Chlorophyll: 5-15 μg/L
        
        # Generate adjacency matrices (fully connected for simplicity)
        adj_matrices = torch.ones(num_samples, num_nodes, num_nodes)
        # Remove self-loops
        for i in range(num_nodes):
            adj_matrices[:, i, i] = 0
        
        # Generate health targets based on environmental factors
        # Health = f(temperature, pH, conductivity, chlorophyll)
        temp_factor = 1.0 - torch.abs(features[:, :, 0] - 27.5) / 10  # Optimal at 27.5°C
        ph_factor = 1.0 - torch.abs(features[:, :, 1] - 7.5) / 2     # Optimal at pH 7.5
        cond_factor = 1.0 - torch.abs(features[:, :, 2] - 500) / 300  # Optimal at 500 μS/cm
        chlor_factor = torch.clamp(features[:, :, 3] / 15, 0, 1)      # Higher is better
        
        health_targets = (temp_factor + ph_factor + cond_factor + chlor_factor) / 4
        health_targets = torch.clamp(health_targets, 0, 1)
        
        # Apply mask to features
        if mask_ratio > 0:
            mask = torch.rand_like(features) > mask_ratio
            features = features * mask.float()
        
        return features.to(self.device), adj_matrices.to(self.device), health_targets.to(self.device)
    
    def train_with_mask(self, mask_ratio: float, epochs: int = 50) -> Dict:
        """
        使用指定mask比例训练模型
        Train model with specified mask ratio
        """
        logger.info(f"Training with mask ratio: {mask_ratio:.1f}")
        
        # Create model
        model = GatedGraphNeuralNetwork(
            input_dim=4,
            hidden_dim=64,
            output_dim=1,
            num_layers=3,
            use_attention=True
        ).to(self.device)
        
        # Setup optimizer and loss
        optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-5)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=10)
        
        # Generate training and validation data
        train_features, train_adj, train_targets = self.generate_masked_data(800, 20, mask_ratio)
        val_features, val_adj, val_targets = self.generate_masked_data(200, 20, mask_ratio)
        
        # Training metrics
        train_losses = []
        val_losses = []
        train_mses = []
        val_mses = []
        
        model.train()
        for epoch in range(epochs):
            # Training phase
            optimizer.zero_grad()
            train_outputs = model(train_features, train_adj)
            train_loss = criterion(train_outputs.squeeze(), train_targets)
            train_loss.backward()
            optimizer.step()
            
            # Validation phase
            model.eval()
            with torch.no_grad():
                val_outputs = model(val_features, val_adj)
                val_loss = criterion(val_outputs.squeeze(), val_targets)
                
                # Calculate MSE
                train_mse = torch.mean((train_outputs.squeeze() - train_targets) ** 2).item()
                val_mse = torch.mean((val_outputs.squeeze() - val_targets) ** 2).item()
            
            model.train()
            
            # Store metrics
            train_losses.append(train_loss.item())
            val_losses.append(val_loss.item())
            train_mses.append(train_mse)
            val_mses.append(val_mse)
            
            # Learning rate scheduling
            scheduler.step(val_loss)
            
            if (epoch + 1) % 10 == 0:
                logger.info(f"Mask {mask_ratio:.1f} - Epoch {epoch+1}/{epochs}: "
                          f"Train MSE: {train_mse:.6f}, Val MSE: {val_mse:.6f}")
        
        return {
            'mask_ratio': mask_ratio,
            'train_losses': train_losses,
            'val_losses': val_losses,
            'train_mses': train_mses,
            'val_mses': val_mses,
            'final_train_mse': train_mses[-1],
            'final_val_mse': val_mses[-1],
            'model_state': model.state_dict()
        }
    
    def run_mask_analysis(self, mask_ratios: List[float] = None, epochs: int = 50):
        """
        运行完整的mask分析
        Run complete mask ratio analysis
        """
        if mask_ratios is None:
            mask_ratios = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        logger.info(f"Starting mask analysis with ratios: {mask_ratios}")
        logger.info(f"Training epochs per mask: {epochs}")
        
        start_time = time.time()
        
        for mask_ratio in mask_ratios:
            result = self.train_with_mask(mask_ratio, epochs)
            self.mask_results[mask_ratio] = result
            
            logger.info(f"Completed mask {mask_ratio:.1f}: "
                       f"Final Val MSE = {result['final_val_mse']:.6f}")
        
        total_time = time.time() - start_time
        logger.info(f"Mask analysis completed in {total_time:.2f} seconds")
        
        return self.mask_results
    
    def visualize_mask_analysis(self, save_path: str = "results/mask_analysis"):
        """
        可视化mask分析结果
        Visualize mask analysis results
        """
        if not self.mask_results:
            logger.warning("No mask analysis results to visualize")
            return
        
        # Create results directory
        os.makedirs(save_path, exist_ok=True)
        
        # Create comprehensive visualization
        fig = plt.figure(figsize=(20, 12))
        gs = gridspec.GridSpec(3, 4, figure=fig, hspace=0.3, wspace=0.3)
        
        # Extract data for plotting
        mask_ratios = sorted(self.mask_results.keys())
        final_train_mses = [self.mask_results[mr]['final_train_mse'] for mr in mask_ratios]
        final_val_mses = [self.mask_results[mr]['final_val_mse'] for mr in mask_ratios]
        
        # 1. MSE vs Mask Ratio (Main Plot)
        ax1 = fig.add_subplot(gs[0, :2])
        ax1.plot(mask_ratios, final_train_mses, 'b-o', linewidth=2, markersize=8, label='Training MSE')
        ax1.plot(mask_ratios, final_val_mses, 'r-s', linewidth=2, markersize=8, label='Validation MSE')
        ax1.set_xlabel('Mask Ratio', fontsize=12)
        ax1.set_ylabel('Final MSE', fontsize=12)
        ax1.set_title('Model Performance vs Mask Ratio', fontsize=14, fontweight='bold')
        ax1.legend(fontsize=11)
        ax1.grid(True, alpha=0.3)
        ax1.set_yscale('log')
        
        # 2. Performance Degradation
        ax2 = fig.add_subplot(gs[0, 2:])
        baseline_mse = final_val_mses[0]  # MSE at mask_ratio = 0
        degradation = [(mse / baseline_mse - 1) * 100 for mse in final_val_mses]
        ax2.bar(mask_ratios, degradation, alpha=0.7, color='orange')
        ax2.set_xlabel('Mask Ratio', fontsize=12)
        ax2.set_ylabel('Performance Degradation (%)', fontsize=12)
        ax2.set_title('Performance Degradation Relative to No Mask', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)
        
        # 3-6. Training curves for selected mask ratios
        selected_masks = [0.0, 0.3, 0.6, 0.9]
        for i, mask_ratio in enumerate(selected_masks):
            if mask_ratio in self.mask_results:
                ax = fig.add_subplot(gs[1, i])
                result = self.mask_results[mask_ratio]
                epochs = range(1, len(result['train_mses']) + 1)
                
                ax.plot(epochs, result['train_mses'], 'b-', linewidth=2, label='Train MSE')
                ax.plot(epochs, result['val_mses'], 'r-', linewidth=2, label='Val MSE')
                ax.set_xlabel('Epoch', fontsize=10)
                ax.set_ylabel('MSE', fontsize=10)
                ax.set_title(f'Training Curves (Mask = {mask_ratio:.1f})', fontsize=12, fontweight='bold')
                ax.legend(fontsize=9)
                ax.grid(True, alpha=0.3)
                ax.set_yscale('log')
        
        # 7. Robustness Analysis
        ax7 = fig.add_subplot(gs[2, :2])
        # Calculate robustness score (inverse of performance degradation)
        robustness_scores = [100 / (1 + deg/100) for deg in degradation]
        ax7.plot(mask_ratios, robustness_scores, 'g-o', linewidth=3, markersize=8)
        ax7.set_xlabel('Mask Ratio', fontsize=12)
        ax7.set_ylabel('Robustness Score', fontsize=12)
        ax7.set_title('Model Robustness to Missing Data', fontsize=14, fontweight='bold')
        ax7.grid(True, alpha=0.3)
        ax7.fill_between(mask_ratios, robustness_scores, alpha=0.3, color='green')
        
        # 8. Summary Statistics
        ax8 = fig.add_subplot(gs[2, 2:])
        ax8.axis('off')
        
        # Calculate summary statistics
        avg_degradation = np.mean(degradation[1:])  # Exclude mask=0
        max_degradation = max(degradation)
        robust_threshold = 0.5  # 50% mask ratio
        robust_idx = next((i for i, mr in enumerate(mask_ratios) if mr >= robust_threshold), -1)
        robust_mse = final_val_mses[robust_idx] if robust_idx >= 0 else None
        
        summary_text = f"""
        MASK ANALYSIS SUMMARY
        ═══════════════════════════════════
        
        📊 Performance Metrics:
        • Baseline MSE (No Mask): {baseline_mse:.6f}
        • Average Degradation: {avg_degradation:.1f}%
        • Maximum Degradation: {max_degradation:.1f}%
        
        🛡️ Robustness Analysis:
        • MSE at 50% Mask: {robust_mse:.6f if robust_mse is not None else 'N/A'}
        • Robustness Score: {robustness_scores[robust_idx]:.1f if robust_idx >= 0 else 'N/A'}
        
        🎯 Model Characteristics:
        • Architecture: Gated Graph Neural Network
        • Hidden Dimensions: 64
        • Number of Layers: 3
        • Training Epochs: {len(result['train_mses'])}
        
        💡 Recommendations:
        • Model shows {'good' if avg_degradation < 50 else 'moderate' if avg_degradation < 100 else 'poor'} robustness
        • Suitable for mask ratios up to {max([mr for mr, deg in zip(mask_ratios, degradation) if deg < 100]):.1f}
        • Consider ensemble methods for higher mask ratios
        """
        
        ax8.text(0.05, 0.95, summary_text, transform=ax8.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))
        
        plt.suptitle('GGNN Model Mask Ratio Analysis', fontsize=16, fontweight='bold', y=0.98)
        
        # Save the plot
        plot_path = os.path.join(save_path, 'mask_analysis_results.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        logger.info(f"Mask analysis visualization saved to: {plot_path}")
        
        plt.show()
        
        return plot_path
    
    def save_results(self, save_path: str = "results/mask_analysis"):
        """
        保存分析结果
        Save analysis results
        """
        os.makedirs(save_path, exist_ok=True)
        
        # Prepare results for JSON serialization
        json_results = {}
        for mask_ratio, result in self.mask_results.items():
            json_results[str(mask_ratio)] = {
                'mask_ratio': result['mask_ratio'],
                'final_train_mse': result['final_train_mse'],
                'final_val_mse': result['final_val_mse'],
                'train_mses': result['train_mses'],
                'val_mses': result['val_mses']
            }
        
        # Save as JSON
        json_path = os.path.join(save_path, 'mask_analysis_results.json')
        with open(json_path, 'w') as f:
            json.dump(json_results, f, indent=2)
        
        logger.info(f"Results saved to: {json_path}")
        
        return json_path

def main():
    """
    主函数 - 运行mask分析
    Main function - Run mask analysis
    """
    print("🎯 GGNN Model Mask Ratio Analysis")
    print("=" * 50)
    
    # Configuration
    config = TrainingConfig(
        learning_rate=0.001,
        batch_size=32,
        epochs=50,
        weight_decay=1e-5,
        dropout=0.1
    )
    
    # Create analyzer
    analyzer = MaskAnalysisTrainer(config)
    
    # Define mask ratios to test
    mask_ratios = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    
    print(f"📊 Testing mask ratios: {mask_ratios}")
    print(f"🔄 Training epochs per mask: {config.epochs}")
    
    # Run analysis
    print("\n🚀 Starting mask analysis...")
    results = analyzer.run_mask_analysis(mask_ratios, config.epochs)
    
    # Visualize results
    print("\n📈 Generating visualizations...")
    plot_path = analyzer.visualize_mask_analysis()
    
    # Save results
    print("\n💾 Saving results...")
    json_path = analyzer.save_results()
    
    print(f"\n✅ Analysis completed!")
    print(f"📊 Visualization: {plot_path}")
    print(f"📄 Results: {json_path}")
    
    return results

if __name__ == "__main__":
    main()
