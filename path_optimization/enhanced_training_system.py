#!/usr/bin/env python3
"""
增强训练系统 - 包含训练曲线和详细可视化
Enhanced Training System with Training Curves and Detailed Visualization

This module extends the intelligent sampling system with:
1. GGNN training process visualization
2. Loss curves and MAE tracking
3. Training metrics monitoring
4. Model performance analysis
5. Convergence analysis

Author: Pearl Farming Monitoring System
Date: 2025-01-26
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import logging
import time
from pathlib import Path
import json

from adaptive_sampling_system import GatedGraphNeuralNetwork, SamplingConfig
from integrated_sampling_system import IntegratedSamplingSystem

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingMetrics:
    """训练指标跟踪器"""
    
    def __init__(self):
        self.train_losses = []
        self.val_losses = []
        self.train_mae = []
        self.val_mae = []
        self.learning_rates = []
        self.epochs = []
        self.convergence_metrics = []
        
    def update(self, epoch: int, train_loss: float, val_loss: float, 
               train_mae: float, val_mae: float, lr: float):
        """更新训练指标"""
        self.epochs.append(epoch)
        self.train_losses.append(train_loss)
        self.val_losses.append(val_loss)
        self.train_mae.append(train_mae)
        self.val_mae.append(val_mae)
        self.learning_rates.append(lr)
        
        # Calculate convergence metric (relative change in validation loss)
        if len(self.val_losses) > 1:
            convergence = abs(self.val_losses[-1] - self.val_losses[-2]) / self.val_losses[-2]
            self.convergence_metrics.append(convergence)
        else:
            self.convergence_metrics.append(1.0)

class EnhancedGGNNTrainer:
    """增强的GGNN训练器"""
    
    def __init__(self, model: GatedGraphNeuralNetwork, config: SamplingConfig):
        self.model = model
        self.config = config
        self.metrics = TrainingMetrics()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
        
        # Training configuration
        self.learning_rate = 0.001
        self.weight_decay = 1e-5
        self.patience = 20
        self.min_delta = 1e-6
        
        # Initialize optimizer and scheduler
        self.optimizer = optim.Adam(self.model.parameters(), 
                                  lr=self.learning_rate, 
                                  weight_decay=self.weight_decay)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=10
        )
        
        logger.info(f"GGNN Trainer initialized on device: {self.device}")
    
    def generate_synthetic_training_data(self, num_samples: int = 1000) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """生成合成训练数据"""
        logger.info(f"Generating {num_samples} synthetic training samples")
        
        # Generate random environmental data
        # Features: [temperature, pH, conductivity, chlorophyll]
        node_features = torch.randn(num_samples, 20, 4)  # 20 nodes per sample
        
        # Generate adjacency matrices (random graphs)
        adjacency_matrices = torch.zeros(num_samples, 20, 20)
        for i in range(num_samples):
            # Create random adjacency matrix
            adj = torch.rand(20, 20)
            adj = (adj > 0.7).float()  # Sparse connectivity
            adj = adj + adj.T  # Make symmetric
            adj.fill_diagonal_(0)  # No self-loops
            adjacency_matrices[i] = adj
        
        # Generate target health indices based on environmental factors
        targets = torch.zeros(num_samples, 20, 1)
        for i in range(num_samples):
            features = node_features[i]
            # Simple health calculation based on optimal ranges
            temp_fitness = torch.exp(-((features[:, 0] - 25.0) ** 2) / (2 * 3.0 ** 2))
            ph_fitness = torch.exp(-((features[:, 1] - 7.5) ** 2) / (2 * 0.5 ** 2))
            cond_fitness = torch.exp(-((features[:, 2] - 500.0) ** 2) / (2 * 100.0 ** 2))
            chlor_fitness = torch.exp(-((features[:, 3] - 10.0) ** 2) / (2 * 5.0 ** 2))
            
            health = temp_fitness * ph_fitness * cond_fitness * chlor_fitness
            targets[i] = health.unsqueeze(1)
        
        return node_features, adjacency_matrices, targets
    
    def train_model(self, num_epochs: int = 100, train_ratio: float = 0.8) -> Dict:
        """训练GGNN模型"""
        logger.info(f"Starting GGNN training for {num_epochs} epochs")
        
        # Generate training data
        node_features, adjacency_matrices, targets = self.generate_synthetic_training_data()
        
        # Split data
        num_train = int(len(node_features) * train_ratio)
        train_features = node_features[:num_train]
        train_adj = adjacency_matrices[:num_train]
        train_targets = targets[:num_train]
        
        val_features = node_features[num_train:]
        val_adj = adjacency_matrices[num_train:]
        val_targets = targets[num_train:]
        
        # Move to device
        train_features = train_features.to(self.device)
        train_adj = train_adj.to(self.device)
        train_targets = train_targets.to(self.device)
        val_features = val_features.to(self.device)
        val_adj = val_adj.to(self.device)
        val_targets = val_targets.to(self.device)
        
        logger.info(f"Training set: {len(train_features)} samples")
        logger.info(f"Validation set: {len(val_features)} samples")
        
        # Training loop
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # Training phase
            self.model.train()
            train_loss, train_mae = self._train_epoch(train_features, train_adj, train_targets)
            
            # Validation phase
            self.model.eval()
            with torch.no_grad():
                val_loss, val_mae = self._validate_epoch(val_features, val_adj, val_targets)
            
            # Update learning rate
            self.scheduler.step(val_loss)
            current_lr = self.optimizer.param_groups[0]['lr']
            
            # Update metrics
            self.metrics.update(epoch, train_loss, val_loss, train_mae, val_mae, current_lr)
            
            # Early stopping check
            if val_loss < best_val_loss - self.min_delta:
                best_val_loss = val_loss
                patience_counter = 0
                # Save best model
                torch.save(self.model.state_dict(), 'best_ggnn_model.pth')
            else:
                patience_counter += 1
            
            # Log progress
            epoch_time = time.time() - start_time
            if epoch % 10 == 0 or epoch == num_epochs - 1:
                logger.info(f"Epoch {epoch:3d}/{num_epochs} | "
                          f"Train Loss: {train_loss:.6f} | Val Loss: {val_loss:.6f} | "
                          f"Train MAE: {train_mae:.6f} | Val MAE: {val_mae:.6f} | "
                          f"LR: {current_lr:.2e} | Time: {epoch_time:.2f}s")
            
            # Early stopping
            if patience_counter >= self.patience:
                logger.info(f"Early stopping at epoch {epoch} (patience: {self.patience})")
                break
        
        # Load best model
        self.model.load_state_dict(torch.load('best_ggnn_model.pth'))
        
        training_summary = {
            'total_epochs': len(self.metrics.epochs),
            'best_val_loss': best_val_loss,
            'final_train_loss': self.metrics.train_losses[-1],
            'final_val_loss': self.metrics.val_losses[-1],
            'final_train_mae': self.metrics.train_mae[-1],
            'final_val_mae': self.metrics.val_mae[-1],
            'convergence_achieved': patience_counter >= self.patience
        }
        
        logger.info("Training completed!")
        logger.info(f"Best validation loss: {best_val_loss:.6f}")
        
        return training_summary
    
    def _train_epoch(self, features: torch.Tensor, adj: torch.Tensor, targets: torch.Tensor) -> Tuple[float, float]:
        """训练一个epoch"""
        total_loss = 0.0
        total_mae = 0.0
        num_batches = 0
        
        # Simple batch processing (process all at once for simplicity)
        self.optimizer.zero_grad()
        
        outputs = self.model(features, adj)
        outputs = torch.sigmoid(outputs)  # Apply sigmoid for health index [0,1]
        
        # Calculate loss
        loss = nn.MSELoss()(outputs, targets)
        mae = nn.L1Loss()(outputs, targets)
        
        # Backward pass
        loss.backward()
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
        self.optimizer.step()
        
        return loss.item(), mae.item()
    
    def _validate_epoch(self, features: torch.Tensor, adj: torch.Tensor, targets: torch.Tensor) -> Tuple[float, float]:
        """验证一个epoch"""
        outputs = self.model(features, adj)
        outputs = torch.sigmoid(outputs)
        
        loss = nn.MSELoss()(outputs, targets)
        mae = nn.L1Loss()(outputs, targets)
        
        return loss.item(), mae.item()
    
    def plot_training_curves(self, save_path: str = "results/training_curves"):
        """绘制训练曲线"""
        Path(save_path).mkdir(parents=True, exist_ok=True)
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('GGNN Training Analysis', fontsize=16, fontweight='bold')
        
        epochs = self.metrics.epochs
        
        # Plot 1: Loss curves
        ax1 = axes[0, 0]
        ax1.plot(epochs, self.metrics.train_losses, 'b-', label='Training Loss', linewidth=2)
        ax1.plot(epochs, self.metrics.val_losses, 'r-', label='Validation Loss', linewidth=2)
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss (MSE)')
        ax1.set_title('Training and Validation Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_yscale('log')
        
        # Plot 2: MAE curves
        ax2 = axes[0, 1]
        ax2.plot(epochs, self.metrics.train_mae, 'b-', label='Training MAE', linewidth=2)
        ax2.plot(epochs, self.metrics.val_mae, 'r-', label='Validation MAE', linewidth=2)
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Mean Absolute Error')
        ax2.set_title('Training and Validation MAE')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Plot 3: Learning rate schedule
        ax3 = axes[0, 2]
        ax3.plot(epochs, self.metrics.learning_rates, 'g-', linewidth=2)
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('Learning Rate')
        ax3.set_title('Learning Rate Schedule')
        ax3.grid(True, alpha=0.3)
        ax3.set_yscale('log')
        
        # Plot 4: Convergence analysis
        ax4 = axes[1, 0]
        if len(self.metrics.convergence_metrics) > 0:
            # Ensure arrays have same length
            conv_epochs = epochs[:len(self.metrics.convergence_metrics)]
            ax4.plot(conv_epochs, self.metrics.convergence_metrics, 'purple', linewidth=2)
            ax4.axhline(y=0.01, color='red', linestyle='--', label='Convergence Threshold')
        ax4.set_xlabel('Epoch')
        ax4.set_ylabel('Relative Change in Val Loss')
        ax4.set_title('Convergence Analysis')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.set_yscale('log')
        
        # Plot 5: Loss difference (overfitting indicator)
        ax5 = axes[1, 1]
        loss_diff = np.array(self.metrics.val_losses) - np.array(self.metrics.train_losses)
        ax5.plot(epochs, loss_diff, 'orange', linewidth=2)
        ax5.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax5.set_xlabel('Epoch')
        ax5.set_ylabel('Val Loss - Train Loss')
        ax5.set_title('Overfitting Indicator')
        ax5.grid(True, alpha=0.3)
        
        # Plot 6: Training summary statistics
        ax6 = axes[1, 2]
        final_metrics = [
            self.metrics.train_losses[-1],
            self.metrics.val_losses[-1],
            self.metrics.train_mae[-1],
            self.metrics.val_mae[-1]
        ]
        metric_names = ['Train\nLoss', 'Val\nLoss', 'Train\nMAE', 'Val\nMAE']
        colors = ['blue', 'red', 'lightblue', 'lightcoral']
        
        bars = ax6.bar(metric_names, final_metrics, color=colors, alpha=0.7)
        ax6.set_ylabel('Final Values')
        ax6.set_title('Final Training Metrics')
        ax6.grid(True, alpha=0.3, axis='y')
        
        # Add value labels on bars
        for bar, value in zip(bars, final_metrics):
            height = bar.get_height()
            ax6.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{value:.4f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        
        # Save the plot
        plot_path = Path(save_path) / 'ggnn_training_curves.png'
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        logger.info(f"Training curves saved to: {plot_path}")
        
        plt.show()
        
        return plot_path
    
    def save_training_metrics(self, save_path: str = "results/training_curves"):
        """保存训练指标"""
        Path(save_path).mkdir(parents=True, exist_ok=True)
        
        metrics_data = {
            'epochs': self.metrics.epochs,
            'train_losses': self.metrics.train_losses,
            'val_losses': self.metrics.val_losses,
            'train_mae': self.metrics.train_mae,
            'val_mae': self.metrics.val_mae,
            'learning_rates': self.metrics.learning_rates,
            'convergence_metrics': self.metrics.convergence_metrics
        }
        
        # Save as JSON
        json_path = Path(save_path) / 'training_metrics.json'
        with open(json_path, 'w') as f:
            json.dump(metrics_data, f, indent=2)
        
        # Save as numpy arrays
        npz_path = Path(save_path) / 'training_metrics.npz'
        np.savez(npz_path, **metrics_data)
        
        logger.info(f"Training metrics saved to: {json_path} and {npz_path}")
        
        return json_path, npz_path
