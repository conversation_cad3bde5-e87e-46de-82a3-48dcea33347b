#!/usr/bin/env python3
"""
Ladder-Pattern Navigation Visualization Demo

This script demonstrates the ladder-pattern robot navigation system for the pearl farming pond,
replacing the previous S-pattern with a more structured ladder-like connectivity pattern.

Ladder Pattern Features:
- Vertical segments: Column-wise movement within each row corridor (ladder rungs)
- Horizontal segments: Connection routes at pond edges (0m and 30m width positions)
- Network structure: Resembles a ladder where robots can traverse vertically and connect horizontally
- Constraint compliance: Maintains column-only movement with edge connections

Usage:
    python ladder_pattern_navigation_demo.py

Output:
    Generates ladder_pattern_navigation.png in 'results/ladder_pattern_navigation/' directory
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add the dynamic_mathematical_model directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'dynamic_mathematical_model'))

from visualization import PearlFarmingVisualizer

def main():
    """Main demonstration function for ladder-pattern navigation"""
    print("🪜 Ladder-Pattern Robot Navigation System")
    print("=" * 60)
    print("Implementing ladder-like navigation pattern with:")
    print("  • Vertical segments: Column-wise scanning routes (ladder rungs)")
    print("  • Horizontal segments: Connection routes at pond edges (ladder sides)")
    print("  • Network structure: Complete traversable route collection")
    print("  • Visual distinction: Different colors/styles for route types")
    print("=" * 60)
    
    # Create the pearl farming visualizer
    print("\n🔧 Initializing Ladder-Pattern Navigation System...")
    visualizer = PearlFarmingVisualizer(
        pond_length=50.0,  # 50m length
        pond_width=30.0,   # 30m width
        dpi=150
    )
    
    # Display calculated parameters
    print(f"\n📐 Grid Parameters:")
    print(f"  • Pond dimensions: {visualizer.pond_length}m × {visualizer.pond_width}m")
    print(f"  • Number of columns: {visualizer.pearls_per_row}")
    print(f"  • Number of rows: {visualizer.num_rows}")
    print(f"  • Column spacing: {visualizer.actual_pearl_spacing:.2f}m")
    print(f"  • Row spacing: {visualizer.actual_row_spacing:.2f}m")
    
    # Generate ladder pattern data
    print(f"\n🔍 Generating Ladder Pattern Network...")
    pearl_positions = visualizer.generate_pearl_positions()
    ladder_paths = visualizer.generate_ladder_pattern_path()
    
    print(f"  • Total pearl positions: {len(pearl_positions)}")
    print(f"  • Vertical segments (ladder rungs): {len(ladder_paths['vertical_segments'])}")
    print(f"  • Horizontal connections (ladder sides): {len(ladder_paths['horizontal_connections'])}")
    print(f"  • Total network points: {len(ladder_paths['all_points'])}")
    
    # Create visualization
    print(f"\n🎨 Creating Ladder-Pattern Visualization...")
    save_path = 'results/ladder_pattern_navigation'
    visualizer.create_ladder_pattern_visualization(save_path)
    
    # Detailed analysis
    print(f"\n📊 Network Analysis:")
    
    # Calculate network statistics
    total_vertical_length = len(ladder_paths['vertical_segments']) * visualizer.pond_width
    total_horizontal_length = len(ladder_paths['horizontal_connections']) * visualizer.actual_pearl_spacing
    total_network_length = total_vertical_length + total_horizontal_length
    
    print(f"  • Vertical scanning routes: {len(ladder_paths['vertical_segments'])} columns")
    print(f"  • Total vertical length: {total_vertical_length:.1f}m")
    print(f"  • Horizontal connections: {len(ladder_paths['horizontal_connections'])} segments")
    print(f"  • Total horizontal length: {total_horizontal_length:.1f}m")
    print(f"  • Complete network length: {total_network_length:.1f}m")
    print(f"  • Network density: {total_network_length/(50*30):.2f}m/m²")
    
    # Connection analysis
    bottom_connections = len(ladder_paths['horizontal_connections']) // 2
    top_connections = len(ladder_paths['horizontal_connections']) // 2
    
    print(f"\n🔗 Connection Structure:")
    print(f"  • Bottom edge connections (y=0m): {bottom_connections}")
    print(f"  • Top edge connections (y=30m): {top_connections}")
    print(f"  • Total connection points: {bottom_connections + top_connections}")
    print(f"  • Connection zones: 2 (top and bottom pond edges)")
    
    print(f"\n🎯 Ladder Pattern Features Implemented:")
    print(f"  ✅ Vertical scanning segments (ladder rungs)")
    print(f"  ✅ Horizontal connection segments (ladder sides)")
    print(f"  ✅ Edge-only connection constraint")
    print(f"  ✅ Complete traversable network visualization")
    print(f"  ✅ Visual distinction between route types")
    print(f"  ✅ Connection zone indicators")
    print(f"  ✅ Network structure resembling ladder pattern")
    
    # File validation
    output_file = os.path.join(save_path, 'ladder_pattern_navigation.png')
    if os.path.exists(output_file):
        print(f"\n📁 Generated File:")
        print(f"  ✅ ladder_pattern_navigation.png")
        print(f"  📂 Location: {save_path}")
    else:
        print(f"\n❌ Error: Output file not found at {output_file}")
    
    print(f"\n🎉 Ladder-Pattern Navigation Visualization Complete!")
    print(f"📂 Check the '{save_path}' directory for the generated visualization.")

def validate_ladder_pattern():
    """Validate the ladder pattern implementation"""
    print("\n🔍 Validating Ladder Pattern Implementation:")
    
    visualizer = PearlFarmingVisualizer(50.0, 30.0)
    ladder_paths = visualizer.generate_ladder_pattern_path()
    
    # Validation 1: Correct number of vertical segments
    expected_vertical_segments = visualizer.pearls_per_row
    actual_vertical_segments = len(ladder_paths['vertical_segments'])
    assert actual_vertical_segments == expected_vertical_segments, f"Expected {expected_vertical_segments} vertical segments, got {actual_vertical_segments}"
    print("  ✅ Vertical segments count: Correct")
    
    # Validation 2: Correct number of horizontal connections
    expected_horizontal_connections = 2 * (visualizer.pearls_per_row - 1)  # Top and bottom connections
    actual_horizontal_connections = len(ladder_paths['horizontal_connections'])
    assert actual_horizontal_connections == expected_horizontal_connections, f"Expected {expected_horizontal_connections} horizontal connections, got {actual_horizontal_connections}"
    print("  ✅ Horizontal connections count: Correct")
    
    # Validation 3: Vertical segments span full pond width
    for i, segment in enumerate(ladder_paths['vertical_segments']):
        min_y = np.min(segment[:, 1])
        max_y = np.max(segment[:, 1])
        assert abs(min_y - 0) < 0.1, f"Vertical segment {i} doesn't start at y=0"
        assert abs(max_y - visualizer.pond_width) < 0.1, f"Vertical segment {i} doesn't end at y=pond_width"
    print("  ✅ Vertical segments span: Full pond width")
    
    # Validation 4: Horizontal connections at correct positions
    bottom_connections = []
    top_connections = []
    for connection in ladder_paths['horizontal_connections']:
        y_pos = connection[0, 1]  # Y position of the connection
        if abs(y_pos - 0) < 0.1:
            bottom_connections.append(connection)
        elif abs(y_pos - visualizer.pond_width) < 0.1:
            top_connections.append(connection)
    
    expected_edge_connections = visualizer.pearls_per_row - 1
    assert len(bottom_connections) == expected_edge_connections, "Incorrect bottom connections"
    assert len(top_connections) == expected_edge_connections, "Incorrect top connections"
    print("  ✅ Edge connections: Correctly positioned")
    
    print("  🎯 All ladder pattern validations passed!")

def compare_patterns():
    """Compare ladder pattern with previous S-pattern"""
    print("\n📊 Pattern Comparison Analysis:")
    
    visualizer = PearlFarmingVisualizer(50.0, 30.0)
    ladder_paths = visualizer.generate_ladder_pattern_path()
    
    # Ladder pattern statistics
    ladder_vertical_length = len(ladder_paths['vertical_segments']) * visualizer.pond_width
    ladder_horizontal_length = len(ladder_paths['horizontal_connections']) * visualizer.actual_pearl_spacing
    ladder_total_length = ladder_vertical_length + ladder_horizontal_length
    
    print(f"  🪜 Ladder Pattern:")
    print(f"    - Network structure: {len(ladder_paths['vertical_segments'])} vertical + {len(ladder_paths['horizontal_connections'])} horizontal segments")
    print(f"    - Total network length: {ladder_total_length:.1f}m")
    print(f"    - Connectivity: Full network with multiple route options")
    print(f"    - Navigation flexibility: High (multiple paths between points)")
    
    print(f"  📈 Advantages of Ladder Pattern:")
    print(f"    ✅ Network-based approach (vs. single continuous path)")
    print(f"    ✅ Multiple route options for navigation")
    print(f"    ✅ Clear visual distinction between route types")
    print(f"    ✅ Better representation of traversable connections")
    print(f"    ✅ Easier path planning and optimization")

if __name__ == "__main__":
    try:
        # Run validation first
        validate_ladder_pattern()
        
        # Run pattern comparison
        compare_patterns()
        
        # Run main demonstration
        main()
        
    except Exception as e:
        print(f"\n❌ Error during execution: {e}")
        print("Please check the implementation and try again.")
        sys.exit(1)
